<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class CountrisTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('countris')->delete();
        
        \DB::table('countris')->insert(array (
            0 => 
            array (
                'id' => 2,
                'Arabic_Name' => 'مصر',
                'English_Name' => 'Egypt',
                'Flag' => 'CountriesImages/0YNgesTQQDPJblD7725Y.png',
                'created_at' => '2022-06-04 19:37:49',
                'updated_at' => '2022-06-06 16:31:13',
                'Safe' => '30',
                'Coin' => '1',
                'Store' => '4',
                'Code' => NULL,
            ),
            1 => 
            array (
                'id' => 3,
                'Arabic_Name' => 'السعوديه',
                'English_Name' => 'Saudi Arabia',
                'Flag' => 'CountriesImages/pgME9vgrOAqViEVToi4i.png',
                'created_at' => '2022-06-04 19:39:05',
                'updated_at' => '2022-06-06 16:31:23',
                'Safe' => '32',
                'Coin' => '4',
                'Store' => '18',
                'Code' => NULL,
            ),
        ));
        
        
    }
}