<?php
use App\Models\CompanyData;
use App\Models\Countris;
use App\Models\Coins;
$Comp=CompanyData::orderBy('id','desc')->first();
$Countris=Countris::all();
use App\Models\ContactUS;
use App\Models\ItemsGroups;
$Contact=ContactUS::orderBy('id','desc')->first();
$Groups=ItemsGroups::whereIn('Store_Show',[1,3])->where('Parent',0)->get();

    if(empty(session()->get('ChangeCountryy'))) {
    
         $Coin=Coins::where('Draw',1)->first();
    $Country=Countris::where('Coin',$Coin->id)->first();
    session()->put('ChangeCountryy',$Country->id);  
      }


use App\Models\MainEComDesign;
$main=MainEComDesign::orderBy('id','desc')->first();

?>





<?php if($main->Body_BG_Type == 1): ?>
    <body style="background: <?php echo e($main->Body_BG_Color); ?>">
<?php else: ?>
        <body style="background-image: url(<?php echo e(URL::to($main->Body_BG_Image)); ?>)">  
<?php endif; ?>        
        <!-- Start Preloader Area -->
        <div class="preloader">
            <div class="loader">
                <div class="sbl-half-circle-spin">
                    <div></div>
                </div>
            </div>
        </div>
        <!-- End Preloader Area -->

        <!-- Start Top Header Area -->
        <div class="top-header-area" style="background: <?php echo e($main->Header_Top_BG_Color); ?>">
            <div class="container">
                <div class="row">
                    <div class="col-lg-4">
                        <div class="top-header-content">
                            <span style="color: <?php echo e($main->Header_Top_Txt_Color); ?>">
                                <i style="color: <?php echo e($main->Header_Top_Txt_Color); ?>" class="flaticon-check"></i>
                             <?php echo e(app()->getLocale() == 'ar' ?$Comp->Address :$Comp->AddressEn); ?>

                            </span>
                        </div>
                    </div>
                    <div class="col-lg-8">
                        <ul class="top-header-optional">
                            <li>
                            <form action="<?php echo e(url('ChangeLang')); ?>" method="get">    
                                <div class="languages-list">
                                    <select name="lang" id="lang" onchange="this.form.submit()">
                                        <option value="en" <?php if(app()->getLocale() == 'en'): ?> selected <?php endif; ?> >English</option>
                                        <option value="ar" <?php if(app()->getLocale() == 'ar'): ?> selected <?php endif; ?>>العربيّة</option>
                                    </select>
                                </div>
                            </form>    
                            </li>
                             <li>
                                 
                                 <form action="<?php echo e(url('ChangeCountrySession')); ?>" method="get">        
                                <div class="languages-list">
                                    <select name="Country" id="coun" onchange="this.form.submit()">
                                             <?php $__currentLoopData = $Countris; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $count): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <option value="<?php echo e($count->id); ?>" <?php if(session()->get('ChangeCountryy') == $count->id): ?> selected <?php endif; ?>><?php echo e(app()->getLocale() == 'ar' ?$count->Arabic_Name :$count->English_Name); ?></option>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                                 </form>     
                            </li>

                              <?php if(empty(auth()->guard('client')->user()->id)): ?>
                            <li>
                                <i style="color: <?php echo e($main->Header_Top_Txt_Color); ?>" class='bx bxs-lock-alt'></i>
                                <span><a style="color: <?php echo e($main->Header_Top_Txt_Color); ?>" href="<?php echo e(url('LoginSite')); ?>"><?php echo e(trans('admin.Login')); ?></a> / <a href="<?php echo e(url('RegisterSite')); ?>"><?php echo e(trans('admin.Register')); ?></a></span>
                            </li>
                            
                            <?php else: ?>
                                  <li>
                                <i style="color: <?php echo e($main->Header_Top_Txt_Color); ?>" class='bx bx-log-out-circle'></i>      
                                <span><a style="color: <?php echo e($main->Header_Top_Txt_Color); ?>" href="<?php echo e(url('LogoutSite')); ?>"> <?php echo e(trans('admin.Logout')); ?> </a></span>
                            </li>
                            
                            
                                      <li>
                                 <i style="color: <?php echo e($main->Header_Top_Txt_Color); ?>" class='bx bx-user-circle'></i>          
                                <span> <a style="color: <?php echo e($main->Header_Top_Txt_Color); ?>" href="<?php echo e(url('MyAccountSite')); ?>"> <?php echo e(trans('admin.MyAccount')); ?> </a></span>
                            </li>
                        <?php endif; ?>    
                            
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Top Header Area -->

        <!-- Start Middle Header Area -->
        <div class="middle-header-area" style="background: <?php echo e($main->Header_Middle_BG_Color); ?>">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-lg-3">
                        <div class="middle-header-logo">
                            <a href="<?php echo e(url('/')); ?>">
                                                <img src="<?php echo e(URL::to($Comp->Logo_Store)); ?>" alt="logo">
                            </a>
                        </div>
                    </div>

                    <div class="col-lg-6">
                        <div class="middle-header-search">
                            <form class="search-form" action="<?php echo e(url('ShopFilterName')); ?>" method="get">
                                <div class="row align-items-center">
                                    <div class="col-md-12">
                                        <div class="search-box">
              <input style="background: <?php echo e($main->Header_SearchBar_BG_Color); ?>; color: <?php echo e($main->Header_SearchBar_Txt_Color); ?>" type="text" name="search" class="form-control" placeholder="<?php echo e(trans('admin.Search_For_Products')); ?>">
                   <button type="submit"><i  class='bx bx-search'></i></button>
                                        </div>
                                    </div>
                             
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="col-lg-3">
                        <ul class="middle-header-optional">
                            
                          <?php if(!empty(auth()->guard('client')->user()->id)): ?>    
                            <li><a href="<?php echo e(url('ComparePage')); ?>"><i style="color: <?php echo e($main->Header_Middle_Icon_Color); ?>" class='bx bx-git-compare'></i></a></li>
                            <li><a href="<?php echo e(url('WishlistPage')); ?>"><i style="color: <?php echo e($main->Header_Middle_Icon_Color); ?>" class="flaticon-heart"></i></a></li>
                            <?php endif; ?>
                            

                            <li>
                                <a href="<?php echo e(url('CartSite')); ?>"><i style="color: <?php echo e($main->Header_Middle_Icon_Color); ?>" class="flaticon-shopping-cart"></i><span id="CartCount"> <?php echo e(Cart::content()->count()); ?></span></a>
                            </li>
                            <li><?php echo e(Cart::total()); ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- End Middle Header Area -->

        <!-- Start Navbar Area -->
        <div class="navbar-area" style="background: <?php echo e($main->Navbar_BG_Color); ?>">
            <div class="main-responsive-nav">
                <div class="container">
                    <div class="main-responsive-menu">
                        <div class="logo">
                            <a href="<?php echo e(url('/')); ?>">
                 
                                <img src="<?php echo e(URL::to($Comp->Logo_Store)); ?>" alt="logo">

                 
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="main-navbar">
                <div class="container">
                    <nav class="navbar navbar-expand-md navbar-light">
                        <div class="navbar-category">
                            <div class="collapse navbar-collapse">
                                <ul class="navbar-nav">
                                    <li class="nav-item">
                                        <a href="#" class="nav-link">
                                            <i class='bx bx-menu'></i>
                                            <?php echo e(trans('admin.Categories')); ?>

                                        </a>
                                        <ul class="dropdown-menu">
                                            
                                        <?php $__currentLoopData = $Groups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                            <li class="nav-item">
                                                <a href="<?php echo e(url('FilterShopCat/'.$group->id)); ?>" class="nav-link">
                                                    <i class="flaticon-desktop-computer"></i>
                                                <?php echo e(app()->getLocale() == 'ar' ?$group->Name :$group->NameEn); ?>

                                                </a>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                          
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="collapse navbar-collapse mean-menu">
                            <ul class="navbar-nav">
                                <li class="nav-item">
                                    <a href="<?php echo e(url('/')); ?>" class="nav-link">
                                        <?php echo e(trans('admin.Home')); ?> 
                                    </a>
                                </li>

                                <li class="nav-item megamenu">
                                    <a href="<?php echo e(url('AboutSite')); ?>" class="nav-link">
                                        <?php echo e(trans('admin.About')); ?> 
                                    </a>
                                    
                                </li>

                                <li class="nav-item">
                                    <a href="<?php echo e(url('ShopSite')); ?>" class="nav-link">
                                        <?php echo e(trans('admin.Shop')); ?> 
                                    </a>
                                </li>

                                <li class="nav-item">
                                    <a href="<?php echo e(url('BlogsSite')); ?>" class="nav-link">
                                        <?php echo e(trans('admin.Blogs')); ?> 
                                    </a>
                                </li>

                                <li class="nav-item">
                                    <a href="<?php echo e(url('ContactSite')); ?>" class="nav-link"><?php echo e(trans('admin.Contact')); ?> </a>
                                </li>
                            </ul>

                    
                        </div>
                    </nav>
                </div>
            </div>

            <div class="others-option-for-responsive">
                <div class="container">
                    <div class="dot-menu">
                        <div class="inner">
                            <div class="circle circle-one"></div>
                            <div class="circle circle-two"></div>
                            <div class="circle circle-three"></div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
        
        
        <!-- End Navbar Area -->
<style>



    .preloader {

        background-color: <?php echo e($main->Preloader_BG_Color); ?> !important;
}
    
    
/*    Small*/
    .preloader .loader .sbl-half-circle-spin div {

    border: 2px solid <?php echo e($main->Preloader_Small_Circle_Color); ?> !important;

}  
/*    Large*/
    .preloader .loader .sbl-half-circle-spin {

    border: 2px solid <?php echo e($main->Preloader_Large_Circle_Color); ?> !important;

}
    
    
    .top-header-optional .languages-list .nice-select{
        
        color: <?php echo e($main->Header_Top_Txt_Color); ?> !important;
        
    }   
    .top-header-optional .languages-list .nice-select::after {
        
         border-color: <?php echo e($main->Header_Top_Txt_Color); ?> !important;
        
    }
    
    .top-header-optional li span a:hover {
        
        color: <?php echo e($main->Header_Top_Txt_Hover_Color); ?> !important;
    }
    
    
    .middle-header-optional li a i:hover {
        color: <?php echo e($main->Header_Middle_Icon_Hover_Color); ?> !important;
    
    }
    
    .middle-header-search form .search-box button {
        
        background: <?php echo e($main->Header_SearchBar_Icon_BG_Color); ?> !important ;
        color: <?php echo e($main->Header_SearchBar_Icon_Txt_Color); ?> !important ;
    }   
    
    .middle-header-search form .search-box button:hover {
        
        background: <?php echo e($main->Header_SearchBar_Icon_Hover_BG_Color); ?> !important ;
        color: <?php echo e($main->Header_SearchBar_Icon_Hover_Txt_Color); ?> !important ;
    }
    
    .main-navbar .navbar .navbar-nav .nav-item a {
        color: <?php echo e($main->Navbar_Txt_Color); ?> !important;
    }  
    .main-navbar .navbar .navbar-nav .nav-item a:hover {
        background: <?php echo e($main->Navbar_Hover_BG_Color); ?> !important;
        color: <?php echo e($main->Navbar_Hover_Txt_Color); ?> !important;
    }
    
    .main-navbar .navbar .navbar-category .navbar-nav .nav-item a {
        
      background: <?php echo e($main->Navbar_Category_BG_Color); ?> !important;
        color: <?php echo e($main->Navbar_Category_Txt_Color); ?> !important;
    }
    
    
    .main-navbar .navbar .navbar-category .navbar-nav .nav-item .dropdown-menu li a {
             background: <?php echo e($main->Navbar_Category_Box_BG_Color); ?> !important;
        color: <?php echo e($main->Navbar_Category_Box_Txt_Color); ?> !important;
        
    }
    
    
        .main-navbar .navbar .navbar-category .navbar-nav .nav-item .dropdown-menu li a:hover {
             background: <?php echo e($main->Navbar_Hover_BG_Color); ?> !important;
        color: <?php echo e($main->Navbar_Hover_Txt_Color); ?> !important;
        
    }
    
    

</style><?php /**PATH C:\xampp\htdocs\ost-erp-online\resources\views/site/layouts/navbar.blade.php ENDPATH**/ ?>