<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCustomerFollowUpsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('customer_follow_ups', function (Blueprint $table) {
            $table->id();
              $table->longText('Code')->nullable();
               $table->longText('Date')->nullable();
               $table->longText('Client')->nullable();
               $table->longText('Subject')->nullable();
               $table->longText('Rate')->nullable();
               $table->longText('Emp')->nullable();
               $table->longText('Visit_Cost')->nullable();
               $table->longText('Note')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('customer_follow_ups');
    }
}
