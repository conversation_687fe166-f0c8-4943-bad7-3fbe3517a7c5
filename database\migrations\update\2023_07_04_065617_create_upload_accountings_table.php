<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUploadAccountingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('upload_accountings', function (Blueprint $table) {
            $table->id();
            $table->longText('Name')->nullable();
            $table->longText('Type')->nullable();
            $table->longText('Parent')->nullable();
            $table->longText('Note')->nullable();
            $table->longText('User')->nullable();
            $table->longText('Account_Code')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('upload_accountings');
    }
}
