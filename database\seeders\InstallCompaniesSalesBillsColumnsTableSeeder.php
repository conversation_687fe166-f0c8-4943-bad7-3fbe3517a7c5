<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class InstallCompaniesSalesBillsColumnsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('install_companies_sales_bills_columns')->delete();
        
        \DB::table('install_companies_sales_bills_columns')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Date' => '1',
                'Code' => '1',
                'Time' => '1',
                'Refrence_Number' => '1',
                'Branch' => '1',
                'Store' => '1',
                'Payment_Method' => '1',
                'Safe' => '1',
                'Type' => '1',
                'Shipping' => '1',
                'Cost_Center' => '1',
                'ShiftCode' => '1',
                'Executor' => '1',
                'User' => '1',
                'Coin' => '1',
                'Due_Date' => '1',
                'Delegate' => '1',
                'Note' => '1',
                'Total_Return' => '1',
                'Total_Price' => '1',
                'Total_Discount' => '1',
                'Total_Tax' => '1',
                'Total_Net' => '1',
                'Paid' => '1',
                'Residual' => '1',
                'Client' => '1',
                'InstallCompany' => '1',
                'ContractNumber' => '1',
                'PayFees' => '1',
                'ServiceFee' => '1',
                'CompanyPrecent' => '1',
                'Product_Code' => '1',
                'Product_Name' => '1',
                'Unit' => '1',
                'Av_Qty' => '1',
                'Qty' => '1',
                'Price' => '1',
                'Discount' => '1',
                'Total_BF_Tax' => '1',
                'Tax' => '1',
                'Total' => '1',
                'Group' => '1',
                'Brand' => '1',
                'Exp_Date' => '1',
                'Product_Store' => '1',
                'created_at' => NULL,
                'updated_at' => '2022-11-10 09:15:55',
            ),
        ));
        
        
    }
}