<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class CitiesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('cities')->delete();
        
        \DB::table('cities')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Arabic_Name' => 'اكتوبر',
                'English_Name' => 'October',
                'Gov' => 2,
                'created_at' => '2021-07-25 23:46:59',
                'updated_at' => '2021-07-25 23:47:23',
                'Ship_Price' => '10',
                'Shipping_Company' => NULL,
            ),
            1 => 
            array (
                'id' => 2,
                'Arabic_Name' => 'Zamalek',
                'English_Name' => 'Zamalek',
                'Gov' => 2,
                'created_at' => '2021-07-25 23:47:11',
                'updated_at' => '2021-07-25 23:47:11',
                'Ship_Price' => '20',
                'Shipping_Company' => NULL,
            ),
            2 => 
            array (
                'id' => 4,
                'Arabic_Name' => 'Maadi',
                'English_Name' => 'Maadi',
                'Gov' => 1,
                'created_at' => '2021-07-25 23:47:57',
                'updated_at' => '2021-07-25 23:47:57',
                'Ship_Price' => '30',
                'Shipping_Company' => NULL,
            ),
            3 => 
            array (
                'id' => 5,
                'Arabic_Name' => 'Nasr City',
                'English_Name' => 'Nasr City',
                'Gov' => 1,
                'created_at' => '2021-07-25 23:48:10',
                'updated_at' => '2021-07-25 23:48:10',
                'Ship_Price' => '40',
                'Shipping_Company' => NULL,
            ),
            4 => 
            array (
                'id' => 6,
                'Arabic_Name' => 'شرم الشيخ',
                'English_Name' => 'شرم الشيخ',
                'Gov' => 4,
                'created_at' => '2022-03-08 01:29:55',
                'updated_at' => '2022-03-08 01:29:55',
                'Ship_Price' => '50',
                'Shipping_Company' => NULL,
            ),
            5 => 
            array (
                'id' => 7,
                'Arabic_Name' => 'الطور',
                'English_Name' => 'الطور',
                'Gov' => 4,
                'created_at' => '2022-03-08 01:30:05',
                'updated_at' => '2022-03-08 01:30:05',
                'Ship_Price' => '70',
                'Shipping_Company' => NULL,
            ),
            6 => 
            array (
                'id' => 8,
                'Arabic_Name' => 'دهب',
                'English_Name' => 'دهب',
                'Gov' => 4,
                'created_at' => '2022-03-08 01:30:56',
                'updated_at' => '2022-03-08 01:30:56',
                'Ship_Price' => '80',
                'Shipping_Company' => NULL,
            ),
        ));
        
        
    }
}