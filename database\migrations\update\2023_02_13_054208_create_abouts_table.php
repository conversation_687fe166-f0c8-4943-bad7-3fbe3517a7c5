<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAboutsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('abouts', function(Blueprint $table)
		{
			$table->bigInteger('id', true);
			$table->text('Image')->nullable();
			$table->text('Image_2')->nullable();
			$table->text('Image_3')->nullable();
			$table->text('Arabic_Title')->nullable();
			$table->text('English_Title')->nullable();
			$table->text('Arabic_Desc')->nullable();
			$table->text('English_Desc')->nullable();
			$table->timestamps(10);
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('abouts');
	}

}
