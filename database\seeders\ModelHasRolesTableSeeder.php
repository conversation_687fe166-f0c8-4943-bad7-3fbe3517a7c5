<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ModelHasRolesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('model_has_roles')->delete();
        
        \DB::table('model_has_roles')->insert(array (
            0 => 
            array (
                'role_id' => 15,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 34,
            ),
            1 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 1,
            ),
            2 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 11,
            ),
            3 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 14,
            ),
            4 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 35,
            ),
            5 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 36,
            ),
            6 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 37,
            ),
            7 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 38,
            ),
            8 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 39,
            ),
            9 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 40,
            ),
            10 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 41,
            ),
            11 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 42,
            ),
            12 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 43,
            ),
            13 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 44,
            ),
            14 => 
            array (
                'role_id' => 18,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 45,
            ),
            15 => 
            array (
                'role_id' => 18,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 46,
            ),
            16 => 
            array (
                'role_id' => 18,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 47,
            ),
            17 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 48,
            ),
            18 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 49,
            ),
            19 => 
            array (
                'role_id' => 15,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 50,
            ),
            20 => 
            array (
                'role_id' => 21,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 53,
            ),
            21 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 54,
            ),
            22 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 55,
            ),
            23 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 56,
            ),
            24 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 57,
            ),
            25 => 
            array (
                'role_id' => 17,
                'model_type' => 'App\\Models\\Admin',
                'model_id' => 58,
            ),
        ));
        
        
    }
}