<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class CompanyDataTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('company_data')->delete();
        
        \DB::table('company_data')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Name' => 'KLAR',
                'Phone1' => '01062201060',
                'Phone2' => '01062201060',
                'Address' => 'EGYPT',
                'Commercial_Record' => '1234',
                'Tax_File_Number' => '1234',
                'Logo' => 'LogoImages/nxVEorVSkrMs0o0HyHXR.png',
                'Icon' => 'IconImages/0Y76KLEMJbn9vp39Qqo5.png',
                'Print_Text' => 'تشرفنا بزيارتكم',
                'created_at' => '2021-08-13 16:30:58',
                'updated_at' => '2022-09-26 04:43:54',
                'Print_Text_Footer' => '0',
                'Seal' => 'LogoImages/HwjMtkrgNiJ0CIPjTj1a.png',
                'Print_Text_Footer_Sales' => '0',
                'Name_Sales_Bill' => 'فاتورة مبيعات',
                'Name_Sales_Order_Bill' => 'امر البيع',
                'Print_Text_Footer_Quote' => '0',
                'Name_Quote_Bill' => 'عرض سعر',
                'Print_Text_Footer_Secretariat' => 'Thank U',
                'Logo_Store' => 'LogoImages/30jupQ53vq2KQFXh3oE4.png',
                'Phone3' => '01062201060',
                'Phone4' => '01062201060',
                'Icon_Store' => 'LogoImages/2xeYvhHGiWUGFsa5UBd7.png',
                'View' => '0',
                'Tax_Registration_Number' => '1',
                'Tax_activity_code' => '1',
                'work_nature' => 'P',
                'Governrate' => '2',
                'City' => '1',
                'Place' => '4',
                'Nationality' => '2',
                'Buliding_Num' => '1',
                'Street' => '1',
                'Postal_Code' => '1',
                'tax_magistrate' => '1',
                'Client_ID' => '1',
                'Serial_Client_ID' => '1',
                'Version_Type' => NULL,
                'Computer_SN' => NULL,
                'Invoice_Type' => NULL,
                'Floor' => NULL,
                'Room' => NULL,
                'Landmark' => NULL,
                'Add_Info' => NULL,
                'Print_Text_Footer_Manufacturing' => NULL,
            ),
        ));
        
        
    }
}