<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShipmentReceiptsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipment_receipts', function (Blueprint $table) {
            $table->id();
              $table->longText('Recived_Store')->nullable();
			$table->longText('Code')->nullable();
			$table->longText('Date')->nullable();
            $table->longText('Total_Cash')->nullable();
            $table->longText('Total_Later')->nullable();
            $table->longText('Total_Price')->nullable();
            $table->longText('Tickets_Numbers')->nullable();
            $table->longText('Status')->nullable();
			$table->longText('ShippingList')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipment_receipts');
    }
}
