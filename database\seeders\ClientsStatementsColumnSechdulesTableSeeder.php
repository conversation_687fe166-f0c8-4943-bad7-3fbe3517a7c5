<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ClientsStatementsColumnSechdulesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('clients_statements_column_sechdules')->delete();
        
        \DB::table('clients_statements_column_sechdules')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Account_Code' => '1',
                'Account_Name' => '1',
                'Debiator_Before' => '1',
                'Creditor_Before' => '1',
                'Total_Debitor' => '1',
                'Total_Creditor' => '1',
                'Debitor_Balance' => '1',
                'Creditor_Balance' => '1',
                'created_at' => NULL,
                'updated_at' => '2022-10-17 13:26:19',
            ),
        ));
        
        
    }
}