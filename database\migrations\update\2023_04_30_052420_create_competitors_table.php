<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCompetitorsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('competitors', function (Blueprint $table) {
            $table->id();
              $table->longText('Name')->nullable();
			$table->longText('Website')->nullable();
			$table->longText('Facebook')->nullable();
			$table->longText('Instagram')->nullable();
			$table->longText('Twitter')->nullable();
			$table->longText('Pinterest')->nullable();
			$table->longText('Addtional_Link')->nullable();
			$table->longText('Phone')->nullable();
			$table->longText('Whatsapp')->nullable();
			$table->longText('Country')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('competitors');
    }
}
