<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ManufactureCompaniesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('manufacture_companies')->delete();
        
        \DB::table('manufacture_companies')->insert(array (
            0 => 
            array (
                'id' => 14,
                'Arabic_Name' => 'Default',
                'English_Name' => 'Default',
                'created_at' => '2022-04-13 02:50:26',
                'updated_at' => '2022-04-13 02:50:26',
            ),
            1 => 
            array (
                'id' => 15,
                'Arabic_Name' => 'Accessories',
                'English_Name' => 'Accessories',
                'created_at' => '2022-06-06 19:15:43',
                'updated_at' => '2022-06-06 19:15:43',
            ),
            2 => 
            array (
                'id' => 16,
                'Arabic_Name' => 'Bag',
                'English_Name' => 'Bag',
                'created_at' => '2022-06-06 19:23:04',
                'updated_at' => '2022-06-06 19:23:04',
            ),
            3 => 
            array (
                'id' => 17,
                'Arabic_Name' => 'Cosmetics',
                'English_Name' => 'Cosmetics',
                'created_at' => '2022-06-06 19:23:24',
                'updated_at' => '2022-06-06 19:23:24',
            ),
            4 => 
            array (
                'id' => 18,
                'Arabic_Name' => 'Fashion',
                'English_Name' => 'Fashion',
                'created_at' => '2022-06-06 19:23:39',
                'updated_at' => '2022-06-06 19:23:39',
            ),
            5 => 
            array (
                'id' => 19,
                'Arabic_Name' => 'Jewellry',
                'English_Name' => 'Jewellry',
                'created_at' => '2022-06-06 19:24:05',
                'updated_at' => '2022-06-06 19:24:05',
            ),
            6 => 
            array (
                'id' => 20,
                'Arabic_Name' => 'Shoes',
                'English_Name' => 'Shoes',
                'created_at' => '2022-06-06 19:24:47',
                'updated_at' => '2022-06-06 19:24:47',
            ),
        ));
        
        
    }
}