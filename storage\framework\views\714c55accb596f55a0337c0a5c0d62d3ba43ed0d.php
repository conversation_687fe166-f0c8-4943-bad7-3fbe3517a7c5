<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <title>OST ERP</title>
  <meta content="" name="description">
  <meta content="" name="keywords">
  <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests" />

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;600;700;800&display=swap" rel="stylesheet">

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" rel="stylesheet">

  <!-- Custom CSS -->
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Open Sans', sans-serif;
      overflow-x: hidden;
      background: linear-gradient(to bottom right, #fffde7, #fff8e1, #fbe9e7);
      color: #333;
      animation: bodyAnimation 10s infinite alternate;
    }

    /* Animation for the body background */
    @keyframes  bodyAnimation {
      0% {
        background: linear-gradient(to bottom right, #fffde7, #fff8e1, #fbe9e7);
      }
      100% {
        background: linear-gradient(to bottom right, #fbe9e7, #fff8e1, #fffde7);
      }
    }

    .hero {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      text-align: center;
      background: #fff8e1;
      padding: 2rem 1rem;
      position: relative;
      animation: heroAnimation 5s ease-in-out;
    }

    /* Hero section animation */
    @keyframes  heroAnimation {
      0% {
        opacity: 0;
        transform: translateY(-20px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .logo-svg svg {
      width: 100%;
      max-width: 300px;
      height: auto;
      animation: logoAnimation 5s infinite;
    }

    .hero .btn-start {
      margin-top: 2rem;
      background-color: #ffd600;
      color: #000;
      border: none;
      padding: 0.75rem 2rem;
      font-weight: 700;
      border-radius: 50px;
      transition: background 0.3s, transform 0.3s, color 0.3s;
    }

    /* Button hover animation */
    .hero .btn-start:hover {
      background-color: #ffea00;
      transform: scale(1.1);
      color: #fff;
    }

    .contact-info {
      margin-top: 3rem;
      text-align: left;
      width: 100%;
    }

    .contact-info h5 {
      font-weight: 700;
      margin-bottom: 1rem;
      color: #ffd600;
      animation: fadeInText 2s ease-in-out;
    }

    .contact-info ul {
      list-style: none;
      padding: 0;
    }

    .contact-info li {
      margin-bottom: 0.75rem;
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 0.95rem;
      color: #333;
    }

    footer {
      background: #fff3e0;
      padding: 1rem;
      text-align: center;
      font-size: 0.875rem;
      color: #777;
      animation: fadeInText 3s ease-in-out;
    }

    @media (min-width: 768px) {
      .hero {
        flex-direction: row;
        justify-content: space-around;
        text-align: left;
      }

      .hero-image img {
        max-width: 500px;
      }
    }

    /* Animation for fading in text */
    @keyframes  fadeInText {
      0% {
        opacity: 0;
      }
      100% {
        opacity: 1;
      }
    }

    /* Animation for logo */
    @keyframes  logoAnimation {
      0% {
        transform: rotate(0deg);
      }
      50% {
        transform: rotate(10deg);
      }
      100% {
        transform: rotate(0deg);
      }
    }
        /* Ensure logo is fully responsive */
    .hero-image img {
      width: 100%;
      max-width: 400px; /* Maximum width */
      height: auto;
    }
  </style>
</head>

<body>
  <section class="hero">
    <div class="hero-image">
      <img src="https://res.cloudinary.com/dy6e9yvs9/image/upload/v1745012170/ost_erp_gglhqk.png" alt="OST ERP logo">
    </div>

    <div class="hero-text">
      <a href="<?php echo e(url('OstAdmin')); ?>" class="btn btn-start">Start Now / ابدأ الان</a>

      <div class="contact-info">
        <h5>Main Office</h5>
        <ul>
          <li><i class="bi bi-geo-alt-fill"></i> Egypt, Cairo</li>
          <li><i class="bi bi-graph-up-arrow"></i> Dr. Eslam Fath Elbab</li>
          <li><i class="bi bi-file-person-fill"></i> Founder & G.M at OST Programming Solutions</li>
          <li><i class="bi bi-telephone-fill"></i> +20 15 5305 4867</li>
          <li><i class="bi bi-envelope-fill"></i> <EMAIL></li>
        </ul>

        <h5>United Arab Emirates</h5>
        <ul>
          <li><i class="bi bi-geo-alt-fill"></i> Abu Dhabi</li>
          <li><i class="bi bi-code-square"></i> Eng. Abdullah El Sayed</li>
          <li><i class="bi bi-file-person-fill"></i> Tech Lead at OST Programming Solutions</li>
          <li><i class="bi bi-telephone-fill"></i> +971 542 799 637</li>
          <li><i class="bi bi-envelope-fill"></i> <EMAIL></li>
        </ul>
        
        <h5>KSA</h5>

      </div>
    </div>
  </section>

  <footer>
    &copy; <?php echo e(date('Y')); ?> <strong>OST ERP</strong>. All Rights Reserved. Designed by <a href="https://ost-erp.online">OST Programming Solutions</a>
  </footer>

  <!-- Bootstrap JS -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html><?php /**PATH C:\xampp\htdocs\ost-erp-online\resources\views/welcome.blade.php ENDPATH**/ ?>