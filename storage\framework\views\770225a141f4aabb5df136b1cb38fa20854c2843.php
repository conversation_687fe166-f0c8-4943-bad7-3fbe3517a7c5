<?php $__env->startSection('content'); ?>
<title><?php echo e(trans('admin.Profile')); ?></title>

  <main id="js-page-content" role="main" class="page-content">
                        <ol class="breadcrumb page-breadcrumb">
                            <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Profile')); ?></a></li>
                            <li class="breadcrumb-item"><?php echo e(trans('admin.Profile')); ?></li>
                            <li class="breadcrumb-item active"><?php echo e(trans('admin.Profile')); ?></li>
                            <li class="position-absolute pos-top pos-right d-none d-sm-block"><span class="js-get-date"></span></li>
                        </ol>
                        <div class="subheader">
                            <h1 class="subheader-title"> <?php echo e(trans('admin.Profile')); ?> </h1>
                        </div>
                        <div class="row">
                            <div class="col-lg-6 col-xl-3 order-lg-1 order-xl-1">
                            <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>     
                                <!-- profile summary -->
                                <div class="card mb-g rounded-top">
                                    <div class="row no-gutters row-grid">
                                        <div class="col-12">
                                            <div class="d-flex flex-column align-items-center justify-content-center p-4">
          <img src="<?php echo e(URL::to(auth()->guard('admin')->user()->image)); ?>" class="rounded-circle shadow-2 img-thumbnail" alt="">
                                                <h5 class="mb-0 fw-700 text-center mt-3">
                                                
                                      
                                                    
                                     <?php echo e(app()->getLocale() == 'ar' ?auth()->guard('admin')->user()->name :auth()->guard('admin')->user()->nameEn); ?>                
                                                </h5>
                             
                                            </div>
                                        </div>
                                      
                                        <div class="col-12">
                                            <div class="p-3 text-center">
                                                <button type="button" class="btn btn-default" data-toggle="modal" data-target="#default-example-modal-lg-edit"><i class="fal fa-edit"></i></button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- photos -->
                            
                                   <!--    
                              <div class="card mb-g">
                                <div class="row row-grid no-gutters">
                                    <div class="col-12">
                                        <div class="p-3">
                                            <h2 class="mb-0 fs-xl">
                                              KPI Employee
                                            </h2>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="p-3">
                                            <div class="fw-500 fs-xs">Staff</div>
                                            <div class="progress progress-xs mt-2">
                                                <div class="progress-bar bg-primary-300 bg-primary-gradient" role="progressbar" style="width: 80%" aria-valuenow="80" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="p-3">
                                            <div class="fw-500 fs-xs">Punctuality</div>
                                            <div class="progress progress-xs mt-2">
                                                <div class="progress-bar bg-primary-300 bg-primary-gradient" role="progressbar" style="width: 100%" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="p-3">
                                            <div class="fw-500 fs-xs">Helpfulness</div>
                                            <div class="progress progress-xs mt-2">
                                                <div class="progress-bar bg-primary-300 bg-primary-gradient" role="progressbar" style="width: 75%" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="p-3">
                                            <div class="fw-500 fs-xs">Knowledge</div>
                                            <div class="progress progress-xs mt-2">
                                                <div class="progress-bar bg-primary-300 bg-primary-gradient" role="progressbar" style="width: 95%" aria-valuenow="95" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="p-3">
                                            <div class="fw-500 fs-xs">Bedside manners</div>
                                            <div class="progress progress-xs mt-2">
                                                <div class="progress-bar bg-danger-300 bg-warning-gradient" role="progressbar" style="width: 30%" aria-valuenow="30" aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                                -->
                            </div>
                            
                            <!--     
                            <div class="col-lg-12 col-xl-6 order-lg-3 order-xl-2">
                             
                            </div>
                            <div class="col-lg-6 col-xl-3 order-lg-2 order-xl-3">

                           <div class="card mb-g">
                            <div class="row row-grid no-gutters">
                                <div class="col-12">
                                    <div class="p-3">
                                        <h2 class="mb-0 fs-xl">
                                            Contacts
                                        </h2>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight">
                                        <span class="profile-image rounded-circle d-block m-auto" style="background-image:url('img/demo/avatars/avatar-b.png'); background-size: cover;"></span>
                                        <span class="d-block text-truncate text-muted fs-xs mt-1">Oliver Kopyov</span>
                                    </a>
                                </div>
                                <div class="col-4">
                                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight">
                                        <span class="profile-image rounded-circle d-block m-auto" style="background-image:url('img/demo/avatars/avatar-c.png'); background-size: cover;"></span>
                                        <span class="d-block text-truncate text-muted fs-xs mt-1">Sesha Gray</span>
                                    </a>
                                </div>
                                <div class="col-4">
                                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight">
                                        <span class="profile-image rounded-circle d-block m-auto" style="background-image:url('img/demo/avatars/avatar-a.png'); background-size: cover;"></span>
                                        <span class="d-block text-truncate text-muted fs-xs mt-1">Preny Amdaney</span>
                                    </a>
                                </div>
                                <div class="col-4">
                                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight">
                                        <span class="profile-image rounded-circle d-block m-auto" style="background-image:url('img/demo/avatars/avatar-e.png'); background-size: cover;"></span>
                                        <span class="d-block text-truncate text-muted fs-xs mt-1">Dr. John Cook PhD</span>
                                    </a>
                                </div>
                                <div class="col-4">
                                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight">
                                        <span class="profile-image rounded-circle d-block m-auto" style="background-image:url('img/demo/avatars/avatar-h.png'); background-size: cover;"></span>
                                        <span class="d-block text-truncate text-muted fs-xs mt-1">Sarah McBrook</span>
                                    </a>
                                </div>
                                <div class="col-4">
                                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight">
                                        <span class="profile-image rounded-circle d-block m-auto" style="background-image:url('img/demo/avatars/avatar-i.png'); background-size: cover;"></span>
                                        <span class="d-block text-truncate text-muted fs-xs mt-1">Jimmy Fellan</span>
                                    </a>
                                </div>
                                <div class="col-4">
                                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight">
                                        <span class="profile-image rounded-circle d-block m-auto" style="background-image:url('img/demo/avatars/avatar-j.png'); background-size: cover;"></span>
                                        <span class="d-block text-truncate text-muted fs-xs mt-1">Arica Grace</span>
                                    </a>
                                </div>
                                <div class="col-4">
                                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight">
                                        <span class="profile-image rounded-circle d-block m-auto" style="background-image:url('img/demo/avatars/avatar-k.png'); background-size: cover;"></span>
                                        <span class="d-block text-truncate text-muted fs-xs mt-1">Jim Ketty</span>
                                    </a>
                                </div>
                                <div class="col-4">
                                    <a href="javascript:void(0);" class="text-center p-3 d-flex flex-column hover-highlight">
                                        <span class="profile-image rounded-circle d-block m-auto" style="background-image:url('img/demo/avatars/avatar-g.png'); background-size: cover;"></span>
                                        <span class="d-block text-truncate text-muted fs-xs mt-1">Ali Grey</span>
                                    </a>
                                </div>
                                <div class="col-12">
                                    <div class="p-3 text-center">
                                        <a href="page_contacts.html" class="btn-link font-weight-bold">View all</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                               
                 
                                <div class="card mb-g">
                                    <div class="row row-grid no-gutters">
                                        <div class="col-12">
                                            <div class="p-3">
                                                <h2 class="mb-0 fs-xl">
                                                Sales Target
                                                </h2>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="p-3">
                                                <h5 class="text-danger">
                                                    Xray improvement algorythm
                                                    <small class="mt-0 mb-3 text-muted">
                                                        Migration of new API to local servers
                                                    </small>
                                                    <span class="badge badge-danger fw-n position-absolute pos-top pos-right mt-3 mr-3">Delayed</span>
                                                </h5>
                                                <div class="row fs-b fw-300">
                                                    <div class="col text-left">
                                                        Progress
                                                    </div>
                                                    <div class="col text-right">
                                                        26%
                                                    </div>
                                                </div>
                                                <div class="progress progress-xs d-flex mb-2 mt-1">
                                                    <span class="progress-bar bg-danger-500 progress-bar-striped" role="progressbar" style="width: 26%" aria-valuenow="26" aria-valuemin="0" aria-valuemax="100"></span>
                                                </div>
                                                <div class="fw-300 mb-3">
                                                    <div class="row">
                                                        <div class="col">
                                                            Budget
                                                        </div>
                                                        <div class="col text-right text-danger">
                                                            -$155,473.70
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="p-3">
                                                <h5>
                                                    Radioactive Isotope Research
                                                    <small class="mt-0 mb-3 text-muted">
                                                        Accelerator based methods of Technetium99m production – target preparation and processing and beam monitoring technologies
                                                    </small>
                                                    <span class="badge badge-primary fw-n position-absolute pos-top pos-right mt-3 mr-3">A</span>
                                                </h5>
                                                <div class="row fs-b fw-300">
                                                    <div class="col text-left">
                                                        Progress
                                                    </div>
                                                    <div class="col text-right">
                                                        90%
                                                    </div>
                                                </div>
                                                <div class="progress progress-xs d-flex mb-2 mt-1">
                                                    <span class="progress-bar bg-primary-500 progress-bar-striped" role="progressbar" style="width: 90%" aria-valuenow="90" aria-valuemin="0" aria-valuemax="100"></span>
                                                </div>
                                                <div class="fw-300 mb-0">
                                                    <div class="row">
                                                        <div class="col">
                                                            Budget
                                                        </div>
                                                        <div class="col text-right">
                                                            $1,325,987.30
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                       
                                    </div>
                                </div>
                            </div>
   -->
                        </div>
      
      
                         <!-- Modal Edit-->
                    <div class="modal fade" id="default-example-modal-lg-edit" tabindex="-1" role="dialog" aria-hidden="true">
                        <div class="modal-dialog modal-lg" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title"><?php echo e(trans('admin.Profile')); ?>  </h5>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true"><i class="fal fa-times"></i></span>
                                    </button>
                                </div>
                                   <div class="modal-body">
       <form action="<?php echo e(url('UpdateAdminProfile/'.auth()->guard('admin')->user()->id)); ?>" method="post" enctype="multipart/form-data">
                                        <?php echo csrf_field(); ?>

                                    <?php echo view('honeypot::honeypotFormFields'); ?>
                            <input type="hidden" name="passwords" value="<?php echo e(auth()->guard('admin')->user()->password); ?>">       
                            <input type="hidden" name="hidden" value="<?php echo e(auth()->guard('admin')->user()->hidden); ?>">       
                            <input type="hidden" name="emp" value="<?php echo e(auth()->guard('admin')->user()->emp); ?>">       
                            <input type="hidden" name="ship" value="<?php echo e(auth()->guard('admin')->user()->ship); ?>">       
                            <input type="hidden" name="vend" value="<?php echo e(auth()->guard('admin')->user()->vend); ?>">       
                            <input type="hidden" name="cli" value="<?php echo e(auth()->guard('admin')->user()->cli); ?>">       
                            <input type="hidden" name="account" value="<?php echo e(auth()->guard('admin')->user()->account); ?>">       
                            <input type="hidden" name="status" value="<?php echo e(auth()->guard('admin')->user()->status); ?>">       
                            <input type="hidden" name="images" value="<?php echo e(auth()->guard('admin')->user()->image); ?>">  
                            <input type="hidden" name="safe" value="<?php echo e(auth()->guard('admin')->user()->safe); ?>">  
                            <input type="hidden" name="store" value="<?php echo e(auth()->guard('admin')->user()->store); ?>">  
                            <input type="hidden" name="type" value="<?php echo e(auth()->guard('admin')->user()->type); ?>">  
                            <input type="hidden" name="roles_name" value="<?php echo e(auth()->guard('admin')->user()->roles_name); ?>">  

                                        <div class="form-row">
                                            <div class="form-group col-lg-6">
                                                <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Arabic_Name')); ?></label>
                        <input type="text" name="name" value="<?php echo e(auth()->guard('admin')->user()->name); ?>"  class="form-control" required>
                                            </div>        
                                            
                                            <div class="form-group col-lg-6">
                                                <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.English_Name')); ?></label>
                        <input type="text" name="nameEn" value="<?php echo e(auth()->guard('admin')->user()->nameEn); ?>"  class="form-control" required>
                                            </div>
                                            <div class="form-group col-lg-6">
                                                <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Email')); ?></label>
                     <input type="email" name="email" value="<?php echo e(auth()->guard('admin')->user()->email); ?>"  class="form-control" required>
                                            </div>
                                         <div class="form-group col-lg-6">
                                <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Password')); ?></label>
                     <input type="password" name="password" value="<?php echo e(old('password')); ?>"  class="form-control" >
                                            </div>
                                              <div class="form-group col-lg-6">
                                <label class="form-label" for="simpleinput"> <?php echo e(trans('admin.Phone')); ?></label>
                     <input type="number" name="phone" value="<?php echo e(auth()->guard('admin')->user()->phone); ?>"  class="form-control">
                                            </div>                
                                            
                                            <div class="form-group col-lg-12">
                                                <label class="form-label"><?php echo e(trans('admin.Image')); ?></label>
                                                <div class="custom-file">
                                      <input type="file" class="custom-file-input" id="customControlValidation7" name="image">
                                  <label class="custom-file-label" for="customControlValidation7">Choose file...</label>
                        
                                                </div>
                                            </div>
                                            <div class="form-group col-lg-12">
                                        <img src="<?php echo e(URL::to(auth()->guard('admin')->user()->image)); ?>" style="height: 200px; max-width: 100%">    
                                            </div>
                                            
                                            
                                        </div>
                                         <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(trans('admin.Close')); ?></button>
                        <button type="submit" class="btn btn-primary"> <?php echo e(trans('admin.Add')); ?></button>
                                </div>                
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
      
      
                    </main>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
     <!-- Optional: page related CSS-->
        <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/fa-brands.css')); ?>">
        <link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/fa-solid.css')); ?>">
<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost-erp-online\resources\views/admin/Profile.blade.php ENDPATH**/ ?>