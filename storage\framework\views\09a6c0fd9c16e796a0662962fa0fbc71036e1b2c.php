   
<?php $__env->startSection('content'); ?>
<?php
use App\Models\MainEComDesign;
use App\Models\SupPagesPartTwoEComDesign;
$main=MainEComDesign::orderBy('id','desc')->first();
$sub=SupPagesPartTwoEComDesign::orderBy('id','desc')->first();
?>

 <style>
.page-title-area {
    background-color: <?php echo e($main->Breadcumb_BG_Color); ?> !important;
}
     
    .login-area {
      background-color: <?php echo e($main->Sub_Page_BG_Color); ?> !important;
} 
     
     .login-form{
         
         background: <?php echo e($sub->Reg_Login_Form_Box_BG_Color); ?> !important;
     }
     
         .login-form h2 {

    color:  <?php echo e($sub->Reg_Login_Form_Box_Title_Color); ?> !important;
     }
     
     .login-form h2::before {

    border-bottom: 1px solid <?php echo e($sub->Reg_Login_Form_Box_Title_Color); ?> !important;
     }
     
     .login-form form .form-group .form-control {
    color: <?php echo e($sub->Reg_Login_Form_Box_Input_Border_Color); ?> !important;
    border: 1px solid <?php echo e($sub->Reg_Login_Form_Box_Input_Border_Color); ?> !important;
     }
     
     .login-form form .lost-your-password a{
         
           color: <?php echo e($sub->Reg_Login_Form_Box_Txt_Color); ?> !important;
     }
     
     
     .login-form form .lost-your-password a::before{
         
         background: <?php echo e($sub->Reg_Login_Form_Box_Txt_Color); ?> !important;
     }
     
       .login-form form .lost-your-password a::before:hover{
         
         background: <?php echo e($sub->Reg_Login_Form_Box_Txt_Hover_Color); ?> !important;
     }
     
     
     .login-form .important-text p a{
         
            color: <?php echo e($sub->Reg_Login_Form_Box_Txt_Color); ?> !important; 
        
         
     }
     
     
          
    .login-form .important-text p{
         
        
            color: <?php echo e($sub->Reg_Login_Form_Box_Txt_Color); ?> !important; 
         
     }
     
     .login-form form button{
         
           background: <?php echo e($sub->Reg_Login_Form_Box_Button_BG_Color); ?> !important;
    color: <?php echo e($sub->Reg_Login_Form_Box_Button_Txt_Color); ?> !important; 
     }
     
     
          .login-form form button:hover{
         
           background: <?php echo e($sub->Reg_Login_Form_Box_Button_BG_Hover_Color); ?> !important;
    color: <?php echo e($sub->Reg_Login_Form_Box_Button_Txt_Hover_Color); ?> !important; 
     }
     
</style>



        <title><?php echo e(trans('admin.Login')); ?></title>

   
        <!-- Start Login Area -->
        <section class="login-area ptb-50">
            <div class="container">
                <div class="login-form">
                    <h2><?php echo e(trans('admin.Login')); ?></h2>

                    <form method="post" action="<?php echo e(url('PostLoginSite')); ?>">
                        <?php echo csrf_field(); ?>
 
                        <div class="form-group">
                            <input type="email" class="form-control"  name="Email" placeholder="<?php echo e(trans('admin.Email')); ?>" id="CustomerEmail" value="<?php echo e(old('Email')); ?>" required>
                        </div>

                        <div class="form-group">
                            <input type="password" class="form-control"  value="<?php echo e(old('Password')); ?>" name="Password" placeholder="<?php echo e(trans('admin.Password')); ?>" required>
                        </div>

                        <div class="row align-items-center">

                            <div class="col-lg-6 col-md-6 col-sm-6 lost-your-password">
                                <a href="<?php echo e(url('ForgotSite')); ?>" class="lost-your-password"><?php echo e(trans('admin.Forgot_your_password')); ?></a>
                            </div>
                        </div>

                        <button type="submit"><?php echo e(trans('admin.Login')); ?></button>
                    </form>

                    <div class="important-text">
                        <p> <?php echo e(trans('admin.Dont_have_an_account')); ?><a href="<?php echo e(url('RegisterSite')); ?>"> <?php echo e(trans('admin.Register')); ?></a></p>
                    </div>
                </div>
            </div>
        </section>
        <!-- End Login Area -->

    
<?php $__env->stopSection(); ?> 

<?php echo $__env->make('site.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost-erp-online\resources\views/site/Login.blade.php ENDPATH**/ ?>