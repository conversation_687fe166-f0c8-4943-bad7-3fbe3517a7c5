<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ContactUSTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('contact_u_s')->delete();
        
        \DB::table('contact_u_s')->insert(array (
            0 => 
            array (
                'id' => 1,
                'Map' => 'www.google.com/maps/embed?pb',
                'Arabic_Title' => 'DROP US A LINE',
                'English_Title' => 'DROP US A LINE',
                'Arabic_Desc' => 'Lorem Ipsum é um texto modelo da indústria tipográfica e de impressão. O Lorem Ipsum tem vindo a ser o texto padrão usado por estas indústrias desde o ano de 1500',
                'English_Desc' => 'Lorem Ipsum é um texto modelo da indústria tipográfica e de impressão. O Lorem Ipsum tem vindo a ser o texto padrão usado por estas indústrias desde o ano de 1500',
                'Opening_Hours' => 'Mon - Sat : 9am - 11pm',
                'Phone1' => '01062201060',
                'Phone2' => '0105206971',
                'Phone_Header' => '01062201060',
                'Email' => '<EMAIL>',
                'Arabic_Address' => 'Cairo,Egypt',
                'English_Address' => 'Cairo,Egypt',
                'created_at' => NULL,
                'updated_at' => '2022-06-04 15:27:21',
            ),
        ));
        
        
    }
}