<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class ItemsGroupsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('items_groups')->delete();
        
        \DB::table('items_groups')->insert(array (
            0 => 
            array (
                'id' => 33,
                'Code' => '1',
                'Name' => 'Accessories',
                'Type' => '0',
                'Parent' => '0',
                'Note' => NULL,
                'Image' => 'ItemsGroupsImages/jv0d3SXirOxVWCaw2KCI.jpeg',
                'created_at' => '2022-04-13 02:47:07',
                'updated_at' => '2022-06-06 19:42:13',
                'Discount' => '0',
            ),
            1 => 
            array (
                'id' => 34,
                'Code' => '2',
                'Name' => 'Bag',
                'Type' => '0',
                'Parent' => '0',
                'Note' => NULL,
                'Image' => 'ItemsGroupsImages/AtUBc9x6GLqdfieDKPRP.jpeg',
                'created_at' => '2022-06-06 19:42:42',
                'updated_at' => '2022-06-06 19:42:42',
                'Discount' => NULL,
            ),
            2 => 
            array (
                'id' => 35,
                'Code' => '3',
                'Name' => 'Cosmetics',
                'Type' => '0',
                'Parent' => '0',
                'Note' => NULL,
                'Image' => 'ItemsGroupsImages/9e1mhDBhoXSoLn7CPe8g.jpeg',
                'created_at' => '2022-06-06 19:43:04',
                'updated_at' => '2022-06-06 19:43:04',
                'Discount' => NULL,
            ),
            3 => 
            array (
                'id' => 36,
                'Code' => '4',
                'Name' => 'Shoes',
                'Type' => '0',
                'Parent' => '0',
                'Note' => NULL,
                'Image' => 'ItemsGroupsImages/heWg9NEvJmcBG8i05FRD.jpeg',
                'created_at' => '2022-06-06 19:43:24',
                'updated_at' => '2022-06-06 19:43:24',
                'Discount' => NULL,
            ),
            4 => 
            array (
                'id' => 37,
                'Code' => '5',
                'Name' => 'Fashion',
                'Type' => '0',
                'Parent' => '0',
                'Note' => NULL,
                'Image' => 'ItemsGroupsImages/pGv7s7N65BK6Ai3Z6pR4.jpeg',
                'created_at' => '2022-06-06 19:43:39',
                'updated_at' => '2022-06-06 19:43:39',
                'Discount' => NULL,
            ),
            5 => 
            array (
                'id' => 38,
                'Code' => '6',
                'Name' => 'Jewellry',
                'Type' => '0',
                'Parent' => '0',
                'Note' => NULL,
                'Image' => 'ItemsGroupsImages/wmeqxM8ns0t3aC7crJgH.jpeg',
                'created_at' => '2022-06-06 19:43:59',
                'updated_at' => '2022-06-06 19:43:59',
                'Discount' => NULL,
            ),
        ));
        
        
    }
}