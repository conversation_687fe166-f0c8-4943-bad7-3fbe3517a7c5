<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Admin;
use App\Models\CompanyData;
use Spatie\Permission\Models\Role;

class ManageSystemSettings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'system:manage
                            {action : The action to perform (view-mode, create-super-admin, list-admins)}
                            {--mode= : View mode (0=Default/Welcome, 1=ECommerce, 2=Restaurant)}
                            {--email= : Admin email}
                            {--name= : Admin name}
                            {--password= : Admin password}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage system settings like view modes and super admins';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'view-mode':
                return $this->handleViewMode();
            case 'create-super-admin':
                return $this->handleCreateSuperAdmin();
            case 'list-admins':
                return $this->handleListAdmins();
            default:
                $this->error('Invalid action. Available actions: view-mode, create-super-admin, list-admins');
                return 1;
        }
    }

    private function handleViewMode()
    {
        $mode = $this->option('mode');

        if ($mode === null) {
            $company = CompanyData::first();
            $currentMode = $company ? $company->View : 'Not set';
            $this->info("Current view mode: $currentMode");
            $this->info("View modes: 0=Default/Welcome, 1=ECommerce, 2=Restaurant");

            $mode = $this->choice('Select new view mode:', [
                'Default/Welcome',
                'ECommerce',
                'Restaurant'
            ]);
            // Convert choice to numeric value
            $modeMap = ['Default/Welcome' => '0', 'ECommerce' => '1', 'Restaurant' => '2'];
            $mode = $modeMap[$mode];
        }

        if (!in_array($mode, ['0', '1', '2'])) {
            $this->error('Invalid mode. Use 0 (Default/Welcome), 1 (ECommerce), or 2 (Restaurant)');
            return 1;
        }

        $company = CompanyData::first();
        if (!$company) {
            $this->error('No company data found. Please create company data first.');
            return 1;
        }

        $company->update(['View' => $mode]);

        $modeNames = ['0' => 'Default/Welcome', '1' => 'ECommerce', '2' => 'Restaurant'];
        $this->info("View mode updated to: {$modeNames[$mode]}");

        return 0;
    }

    private function handleCreateSuperAdmin()
    {
        $email = $this->option('email') ?: $this->ask('Enter admin email');
        $name = $this->option('name') ?: $this->ask('Enter admin name');
        $password = $this->option('password') ?: $this->secret('Enter admin password');

        if (Admin::where('email', $email)->exists()) {
            $this->error("Admin with email $email already exists");
            return 1;
        }

        $admin = Admin::create([
            'name' => $name,
            'email' => $email,
            'password' => bcrypt($password),
            'roles_name' => 'Owner',
            'status' => 1,
            'hidden' => 0
        ]);

        $admin->assignRole('Owner');

        $this->info("Super admin created successfully!");
        $this->info("Email: $email");
        $this->info("Name: $name");
        $this->info("Role: Owner");

        return 0;
    }

    private function handleListAdmins()
    {
        $admins = Admin::with('roles')->get();

        if ($admins->isEmpty()) {
            $this->info('No admins found.');
            return 0;
        }

        $this->info('Current Admins:');
        $this->table(
            ['ID', 'Name', 'Email', 'Role', 'Status'],
            $admins->map(function ($admin) {
                return [
                    $admin->id,
                    $admin->name,
                    $admin->email,
                    $admin->roles_name,
                    $admin->status ? 'Active' : 'Inactive'
                ];
            })
        );

        return 0;
    }
}
