<?php $__env->startSection('content'); ?>
<?php
use App\Models\Modules;
$Modules=Modules::orderBy('id','desc')->first();
?>
<style>
    .data-def .img-fluid{
        max-height:50px!important;
    }
</style>
<title><?php echo e(trans('admin.Default_Data')); ?></title>
<link rel="stylesheet" media="screen, print" href="css/formplugins/summernote/summernote.css">
<main id="js-page-content" role="main" class="page-content">
   <ol class="breadcrumb page-breadcrumb">
      <li class="breadcrumb-item"><a href="javascript:void(0);"><?php echo e(trans('admin.Settings')); ?></a></li>
      <li class="breadcrumb-item active"> <?php echo e(trans('admin.Default_Data')); ?>   </li>
      <li class="position-absolute pos-top pos-right d-none d-sm-block"><span
         class="js-get-date"></span></li>
   </ol>
   <!-- data entry -->
   <div class="row">
      <div class="col-lg-12">
         <div id="panel-2" class="panel">
            <div class="panel-hdr">
            </div>
            <div class="panel-container show">
               <span id="ex"> <?php echo $__env->make('admin.layouts.messages', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>     
           
               <div class="panel-content">
                  <ul class="nav nav-tabs" role="tablist">
                
                      
                              <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-8" role="tab">   <?php echo e(trans('admin.Company_Data')); ?>  </a>
                     </li>
                     <?php if($Modules->Accounts  ==  1): ?>    
                     <li class="nav-item">
                        <a class="nav-link active" data-toggle="tab" href="#tab_borders_icons-1" role="tab">  <?php echo e(trans('admin.Accounts')); ?>   </a>
                     </li>
                     <?php endif; ?>
                     <?php if($Modules->Stores  ==  1): ?>  
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-2" role="tab"> <?php echo e(trans('admin.Stores')); ?>  </a>
                     </li>
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-3" role="tab">  <?php echo e(trans('admin.Purchases_P')); ?>  </a>
                     </li>
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-4" role="tab">  <?php echo e(trans('admin.Sales')); ?>  </a>
                     </li>
                     <?php endif; ?>
                     <?php if($Modules->CRM  ==  1): ?>
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-5" role="tab">  <?php echo e(trans('admin.CRM')); ?>  </a>
                     </li>
                     <?php endif; ?>
             
                     <?php if($Modules->Maintenance  ==  1): ?>
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-10" role="tab">   <?php echo e(trans('admin.Maintaince')); ?>  </a>
                     </li>
                     <?php endif; ?>
                      
                         <?php if($Modules->Manufacturing  ==  1): ?>
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-11" role="tab">   <?php echo e(trans('admin.Manufacturing')); ?>  </a>
                     </li>
                     <?php endif; ?>
                               <?php if($Modules->Shipping  ==  1): ?>
                      
                         <li class="nav-item">
                                                <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-6" role="tab">  <?php echo e(trans('admin.Shipping')); ?>  </a>
                                            </li>
                      <?php endif; ?>
              
                      
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-9" role="tab">   <?php echo e(trans('admin.Show_Hide_Data')); ?>  </a>
                     </li>    
                      
                               <?php if($Modules->Stores  ==  1): ?>  
                     <li class="nav-item">
                        <a class="nav-link" data-toggle="tab" href="#tab_borders_icons-7" role="tab">   <?php echo e(trans('admin.Print')); ?>  </a>
                     </li>
                      <?php endif; ?>
                      
                  </ul>
                  <div class="tab-content border border-top-0 p-3">
                     <div class="tab-pane fade show active" id="tab_borders_icons-1" role="tabpanel">
                        <?php if(!empty($Accounts)): ?>          
                        <form action="<?php echo e(url('AddDefaultAccount')); ?>" method="post"  class="form-row">
                           <?php echo csrf_field(); ?>  
                           <?php echo view('honeypot::honeypotFormFields'); ?>
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-lg-4">
                                       <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                                       <select class="select2 form-control w-100" name="Coin" required>
                                          <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                          <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                          <option value="<?php echo e($coin->id); ?>" <?php if($coin->id  == $Accounts->Coin): ?> selected <?php endif; ?>>  
                                     <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?>          
                                          </option>
                                          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                       </select>
                                    </div>
                                    <div class="form-group col-lg-4">
                                       <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Draw')); ?></label>
                                       <input type="text" name="Draw" value="<?php echo e($Accounts->Draw); ?>" class="form-control" required>
                                    </div>
                                              <div class="form-group col-lg-4">
                              <label class="form-label" for=""><?php echo e(trans('admin.Sure_Recipts')); ?></label>
                              <select class="select2 form-control w-100" name="Sure_Recipts"  required>
                              <option value="1" <?php if($Accounts->Sure_Recipts == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?> </option>
                              <option value="0" <?php if( $Accounts->Sure_Recipts == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                              </select>
                           </div>
                                     
                                                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Show_Group')); ?></label>
                              <select class="select2 form-control w-100" name="Show_Group"  required>
                              <option value="1" <?php if($Accounts->Show_Group == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $Accounts->Show_Group == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>      
                                     
                                     <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Account_Balance')); ?></label>
                              <select class="select2 form-control w-100" name="Account_Balance"  required>
                              <option value="1" <?php if($Accounts->Account_Balance == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $Accounts->Account_Balance == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                     
                                     <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Slaray_Method')); ?></label>
                              <select class="select2 form-control w-100" name="Salary"  required>
                              <option value="1" <?php if($Accounts->Salary == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.EmpAccount')); ?> </option>
                              <option value="2" <?php if( $Accounts->Salary == 2): ?> selected <?php endif; ?>><?php echo e(trans('admin.WorthAccount')); ?></option>
                              </select>
                           </div>
                                     
                                     
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Commission_Method')); ?></label>
                              <select class="select2 form-control w-100" name="Commission"  required>
                              <option value="1" <?php if($Accounts->Commission == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.CommissionAccount')); ?> </option>
                              <option value="2" <?php if( $Accounts->Commission == 2): ?> selected <?php endif; ?>><?php echo e(trans('admin.WorthAccount')); ?></option>
                              </select>
                           </div>
                                 
                                     
                                     

                                     
                                     
                                 </div>
                                 <div class="form-row mt-2">
                                    <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Save')); ?></button>
                                 </div>
                              </div>
                           </div>
                        </form>
                        <?php else: ?>
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="<?php echo e(url('AddDefaultAccountsFirst')); ?>">
                                       <?php echo e(trans('admin.AddNew')); ?>

                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <?php endif; ?>            
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-2" role="tabpanel">
                        <?php if(!empty($Stores)): ?>          
                        <form action="<?php echo e(url('AddDefaultStore')); ?>" method="post"  class="form-row">
                           <?php echo csrf_field(); ?>  
                           <?php echo view('honeypot::honeypotFormFields'); ?>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Store')); ?></label>
                              <select class="select2 form-control w-100" id="" name="Store" required>
                                 <option value=""> <?php echo e(trans('admin.Store')); ?></option>
                                 <?php $__currentLoopData = $Storess; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($store->id); ?>" <?php if($store->id  == $Stores->Store): ?> selected <?php endif; ?>>
                                  
                                 <?php echo e(app()->getLocale() == 'ar' ?$store->Name :$store->NameEn); ?>     
                                  </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                              <select class="select2 form-control w-100" name="Coin" required>
                                 <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                 <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($coin->id); ?>" <?php if($coin->id  == $Stores->Coin): ?> selected <?php endif; ?>>
                                  
                                 <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?>   
                                  </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?> 
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Account_Dificit')); ?></label>
                              <select  class="js-data-example-ajax form-control w-100" name="Account_Dificit" id="AccountDificit" required>
                                 <?php if(!empty($Stores->Account_Dificit)): ?>    
                                 <option value="<?php echo e($Stores->Account_Dificit); ?>">
                                  
                         <?php echo e(app()->getLocale() == 'ar' ?$Stores->Account_Dificit()->first()->Name :$Stores->Account_Dificit()->first()->NameEn); ?>          
                                  </option>
                                 <?php endif; ?>    
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Account_Excess')); ?></label>
                              <select  class="js-data-example-ajax form-control w-100" name="Account_Excess" id="AccountExcess" required>
                                 <?php if(!empty($Stores->Account_Excess)): ?>     
                                 <option value="<?php echo e($Stores->Account_Excess); ?>">
                                  
                                 <?php echo e(app()->getLocale() == 'ar' ?$Stores->Account_Excess()->first()->Name :$Stores->Account_Excess()->first()->NameEn); ?>          
                                  </option>
                                 <?php endif; ?>
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Group')); ?>  </label><span class="strick">*</span>
                              <select class="select2 form-control w-100" name="Group" required>
                                 <option value=""><?php echo e(trans('admin.Group')); ?></option>
                                 <?php $__currentLoopData = $ItemsGroups; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <option value="<?php echo e($group->id); ?>" <?php if($group->id  == $Stores->Group): ?> selected <?php endif; ?>>
                                   <?php echo e(app()->getLocale() == 'ar' ?$group->Name :$group->NameEn); ?>

                                  </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for=""> <?php echo e(trans('admin.Unit')); ?> </label>            
                              <select class="select2 form-control w-100" name="Unit">
                                 <option value=""><?php echo e(trans('admin.Unit')); ?></option>
                                 <?php $__currentLoopData = $Units; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $uni): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <option value="<?php echo e($uni->id); ?>" <?php if($uni->id  == $Stores->Unit): ?> selected <?php endif; ?>>
                                     <?php echo e(app()->getLocale() == 'ar' ?$uni->Name :$uni->NameEn); ?>

                                  </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for=""> <?php echo e(trans('admin.Tax')); ?> </label>
                              <select class="select2 form-control w-100" name="Tax">
                                 <option value=""><?php echo e(trans('admin.Tax')); ?></option>
                                 <?php $__currentLoopData = $Taxes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tax): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($tax->id); ?>" <?php if($tax->id  == $Stores->Tax): ?> selected <?php endif; ?>>
                                  
                                     <?php echo e(app()->getLocale() == 'ar' ?$tax->Name :$tax->NameEn); ?>

                                  </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for=""> 
                              <?php echo e(trans('admin.Product_Type_Default')); ?>    
                              </label><span class="strick">*</span>
                              <select class="select2 form-control w-100"  name="Type"  >
                                 <option value=""><?php echo e(trans('admin.Product_Type')); ?> </option>
                                 <option value="Completed" <?php if($Stores->Type  == 'Completed'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Completed')); ?> </option>
                                 <option value="Raw" <?php if($Stores->Type  == 'Raw'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Raw')); ?> </option>
                                 <option value="Service" <?php if($Stores->Type  == 'Service'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Service')); ?></option>
                                 <option value="Subscribe" <?php if($Stores->Type  == 'Subscribe'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Subscribe')); ?></option>
                                 <option value="Assembly" <?php if($Stores->Type  == 'Assembly'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Assembly')); ?></option>
                                 <option value="Industrial" <?php if($Stores->Type  == 'Industrial'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Industrial')); ?></option>
                                 <option value="Single_Variable" <?php if($Stores->Type  == 'Single_Variable'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Single_Variable')); ?></option>
                                 <option value="Duble_Variable" <?php if($Stores->Type  == 'Duble_Variable'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Duble_Variable')); ?></option>
                                 <option value="Serial" <?php if($Stores->Type  == 'Serial'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Serial')); ?></option>
                                 <option value="Petroll" <?php if($Stores->Type  == 'Petroll'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Petroll')); ?></option>
                                 <option value="Variable_Aggregate" <?php if($Stores->Type  == 'Variable_Aggregate'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Variable_Aggregate')); ?></option>
                                 <option value="Additions" <?php if($Stores->Type  == 'Additions'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Additions')); ?></option>
                              </select>
                           </div>
                             <div class="form-group col-md-3">
                              <label class="form-label" for=""> <?php echo e(trans('admin.StoresTarnsferPrice')); ?>  </label>
                              <select class="select2 form-control w-100" name="StoresTarnsferPrice">
            <option value="1" <?php if($Stores->StoresTarnsferPrice == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
            <option value="0" <?php if($Stores->StoresTarnsferPrice == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                                
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Guide_Product_Cost')); ?>   </label>
                              <select class="select2 form-control w-100" name="Guide_Product_Cost">
                                 <option value="1" <?php if($Stores->Guide_Product_Cost == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                                  
                                 <option value="0" <?php if($Stores->Guide_Product_Cost == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                                
                              </select>
                           </div>
                      
                            <div class="form-group col-md-3">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Client_Store_Account')); ?>     </label>
                              <select class="select2 form-control w-100" name="Client_Store_Account">
                                 <option value="1" <?php if($Stores->Client_Store_Account == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                                  
                                 <option value="0" <?php if($Stores->Client_Store_Account == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                                
                              </select>
                           </div>
                            
                            
                                  <div class="form-group col-md-3">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Show_Ship')); ?>     </label>
                              <select class="select2 form-control w-100" name="Show_Ship">
                                 <option value="1" <?php if($Stores->Show_Ship == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                                  
                                 <option value="0" <?php if($Stores->Show_Ship == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                                
                              </select>
                           </div>
                            
                          <div class="form-group col-md-2">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Code_Typeee')); ?> </label>
                       <select class="select2 form-control w-100" name="CodeType" required>
           <option value=""><?php echo e(trans('admin.Code_Typeee')); ?></option>
           <option value="GS1" <?php if($Stores->CodeType == 'GS1'): ?> selected <?php endif; ?>>GS1</option>
           <option value="EGS" <?php if($Stores->CodeType == 'EGS'): ?> selected <?php endif; ?>>EGS</option>
                                       </select>
                                    </div>
                                     
               
                           <div class="form-group col-md-2">
                              <label class="form-label" for=""> <?php echo e(trans('admin.Style')); ?></label>
                              <select class="select2 form-control w-100" name="Style" required>
                                 <option value=""><?php echo e(trans('admin.Style')); ?></option>
                                 <?php $__currentLoopData = $Settings; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sett): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($sett->id); ?>" <?php if($Stores->Style == $sett->id): ?> selected <?php endif; ?>>
                                  
                                    <?php echo e(app()->getLocale() == 'ar' ?$sett->Name :$sett->NameEn); ?>

                                  </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                              </select>
                           </div>
                            
                            
                                                   <div class="form-group col-md-2">
                              <label class="form-label" for=""> <?php echo e(trans('admin.StoresTarnsfer')); ?>  </label>
                              <select class="select2 form-control w-100" name="StoresTarnsferHide">
            <option value="1" <?php if($Stores->StoresTarnsferHide == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?></option>
            <option value="0" <?php if($Stores->StoresTarnsferHide == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                                
                              </select>
                           </div>
                            
                            
                                                   <div class="form-group col-md-3">
                              <label class="form-label" for=""> <?php echo e(trans('admin.ReturnStoresTransfer')); ?>  </label>
                              <select class="select2 form-control w-100" name="ReturnStoresTransfer">
            <option value="1" <?php if($Stores->ReturnStoresTransfer == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
            <option value="0" <?php if($Stores->ReturnStoresTransfer == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                                
                              </select>
                           </div>
                            
                                                   <div class="form-group col-md-3">
                              <label class="form-label" for=""> <?php echo e(trans('admin.Cost_Price')); ?>  </label>
                              <select class="select2 form-control w-100" name="Cost_Price">
            <option value="1" <?php if($Stores->Cost_Price == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Average')); ?></option>
            <option value="0" <?php if($Stores->Cost_Price == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Last_Purch_Price')); ?></option>
            <option value="2" <?php if($Stores->Cost_Price == 2): ?> selected <?php endif; ?>>FIFO</option>
                                
                              </select>
                           </div>
                            
                            

                            
                           <div class="form-group col-md-6">
                              <label class="form-label" for=""> 
                              <?php echo e(trans('admin.Product_Type_Use')); ?>    
                              </label><span class="strick">*</span>
                              <select class="select2 form-control w-100" multiple name="P_Type[]"  >
                                 <option value=""><?php echo e(trans('admin.Product_Type')); ?> </option>
                                 <option value="Completed"
                                                   <?php $__currentLoopData = $ProductType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                         <?php if($type->Type == 'Completed'): ?>
                                         selected
                                         <?php endif; ?>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                         
                                         ><?php echo e(trans('admin.Completed')); ?> </option>
                                 <option value="Raw"
                                                      <?php $__currentLoopData = $ProductType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                         <?php if($type->Type == 'Raw'): ?>
                                         selected
                                         <?php endif; ?>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                         
                                         ><?php echo e(trans('admin.Raw')); ?> </option>
                                 <option value="Service"
                                                      <?php $__currentLoopData = $ProductType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                         <?php if($type->Type == 'Service'): ?>
                                         selected
                                         <?php endif; ?>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                         
                                         ><?php echo e(trans('admin.Service')); ?></option>
                                 <option value="Subscribe"
                                                      <?php $__currentLoopData = $ProductType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                         <?php if($type->Type == 'Subscribe'): ?>
                                         selected
                                         <?php endif; ?>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                         
                                         ><?php echo e(trans('admin.Subscribe')); ?></option>
                                 <option value="Assembly"
                                                      <?php $__currentLoopData = $ProductType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                         <?php if($type->Type == 'Assembly'): ?>
                                         selected
                                         <?php endif; ?>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                         
                                         ><?php echo e(trans('admin.Assembly')); ?></option>
                                 <option value="Industrial"
                                                      <?php $__currentLoopData = $ProductType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                         <?php if($type->Type == 'Industrial'): ?>
                                         selected
                                         <?php endif; ?>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                         
                                         ><?php echo e(trans('admin.Industrial')); ?></option>
                                 <option value="Single_Variable"
                                                      <?php $__currentLoopData = $ProductType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                         <?php if($type->Type == 'Single_Variable'): ?>
                                         selected
                                         <?php endif; ?>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                         
                                         ><?php echo e(trans('admin.Single_Variable')); ?></option>
                                 <option value="Duble_Variable"
                                                      <?php $__currentLoopData = $ProductType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                         <?php if($type->Type == 'Duble_Variable'): ?>
                                         selected
                                         <?php endif; ?>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                         
                                         ><?php echo e(trans('admin.Duble_Variable')); ?></option>
                                 <option value="Serial"
                                                      <?php $__currentLoopData = $ProductType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                         <?php if($type->Type == 'Serial'): ?>
                                         selected
                                         <?php endif; ?>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                         
                                         ><?php echo e(trans('admin.Serial')); ?></option>
                                 <option value="Petroll"
                                                      <?php $__currentLoopData = $ProductType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                         <?php if($type->Type == 'Petroll'): ?>
                                         selected
                                         <?php endif; ?>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                         
                                         ><?php echo e(trans('admin.Petroll')); ?></option>
                                 <option value="Variable_Aggregate"
                                                      <?php $__currentLoopData = $ProductType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                         <?php if($type->Type == 'Variable_Aggregate'): ?>
                                         selected
                                         <?php endif; ?>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                         
                                         ><?php echo e(trans('admin.Variable_Aggregate')); ?></option>
                                 <option value="Additions"
                                                      <?php $__currentLoopData = $ProductType; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                         <?php if($type->Type == 'Additions'): ?>
                                         selected
                                         <?php endif; ?>
                                         <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                         
                                         ><?php echo e(trans('admin.Additions')); ?></option>
                              </select>
                           </div>
                         
                            
              
                            
                           <div class="form-group col-md-6">
                              <label class="form-label" for="multiple-label">
                              <?php echo e(trans('admin.Show_in_Print')); ?>

                              </label>
                              <select class="select2 form-control" multiple="multiple" id="multiple-label" name="Show[]">
                                 <optgroup label="Mountain Time Zone">
                                    <option value="CompanyName" <?php if($Print->CompanyName == 1): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Company_Name')); ?> </option>
                                    <option value="ProductName" <?php if($Print->ProductName == 1): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Product_Name')); ?> </option>
                                    <option value="ProductPrice" <?php if($Print->ProductPrice == 1): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Product_Price')); ?> </option>
                                    <option value="Coin" <?php if($Print->Coin == 1): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Coin')); ?> </option>
                                    <option value="Unit" <?php if($Print->Unit == 1): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Unit')); ?> </option>
                                    <option value="Group" <?php if($Print->Group == 1): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Group')); ?> </option>
                                    <option value="Code" <?php if($Print->Code == 1): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Code')); ?> </option>
                                    <option value="Logo" <?php if($Print->Logo == 1): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Logo')); ?> </option>
                                 </optgroup>
                              </select>
                           </div>
                           <div class="form-row">
                              <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Save')); ?></button>
                           </div>
                        </form>
                        <?php else: ?>
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="<?php echo e(url('AddDefaultStoreFirst')); ?>">
                                       <?php echo e(trans('admin.AddNew')); ?>

                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <?php endif; ?>        
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-3" role="tabpanel">
                        <?php if(!empty($Purchases)): ?>  
                        <form action="<?php echo e(url('AddDefaultPurchases')); ?>" method="post" enctype="multipart/form-data" class="form-row">
                           <?php echo csrf_field(); ?>

                           <?php echo view('honeypot::honeypotFormFields'); ?>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Store')); ?></label>
                              <select class="select2 form-control w-100"  name="Store" required>
                                 <option value=""> <?php echo e(trans('admin.Store')); ?></option>
                                 <?php $__currentLoopData = $Storess; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($stor->id); ?>" <?php if($stor->id  == $Purchases->Store): ?> selected <?php endif; ?>>
                           
                                        <?php echo e(app()->getLocale() == 'ar' ?$stor->Name :$stor->NameEn); ?>     
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Safe')); ?></label>
                              <select class="select2 form-control w-100" name="Safe" required>
                                 <option value=""> <?php echo e(trans('admin.Safe')); ?></option>
                                 <?php $__currentLoopData = $Safes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $safe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($safe->id); ?>" <?php if($safe->id  == $Purchases->Safe): ?> selected <?php endif; ?>>
                             
                                  <?php echo e(app()->getLocale() == 'ar' ?$safe->Name :$safe->NameEn); ?>        
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                              <select class="select2 form-control w-100" name="Coin" required>
                                 <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                 <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($coin->id); ?>" <?php if($Purchases->Coin == $coin->id): ?> selected <?php endif; ?>>
                             <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?> 
                                     
                                     
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""> <?php echo e(trans('admin.Payment_Method')); ?></label>
                              <select class="select2 form-control w-100" name="Payment_Method"  required>
                              <option value="Cash" <?php if($Purchases->Payment_Method == 'Cash'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Cash')); ?> </option>
                              <option value="Later" <?php if($Purchases->Payment_Method == 'Later'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Later')); ?></option>
                              <option value="Check" <?php if($Purchases->Payment_Method == 'Check'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Check')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Status')); ?></label>
                              <select class="select2 form-control w-100" name="Status"    required>
                              <option value="1" <?php if($Purchases->Status == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Recived')); ?> </option>
                              <option value="0" <?php if( $Purchases->Status == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Pending')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Delegate')); ?></label>
                              <select class="select2 form-control w-100" name="Delegate">
                                 <option value=""> <?php echo e(trans('admin.Delegate')); ?></option>
                                 <?php $__currentLoopData = $Employesss; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $emp): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($emp->id); ?>" <?php if($emp->id  == $Purchases->Delegate): ?> selected <?php endif; ?>>
                    
                                 <?php echo e(app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn); ?>     
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Account')); ?></label>
                              <select class="select2 form-control w-100" id="vendor" name="Vendor" required>
                                 <option value=""> <?php echo e(trans('admin.Account')); ?></option>
                                 <?php $__currentLoopData = $Vendors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vend): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($vend->id); ?>" <?php if($vend->id  == $Purchases->Vendor): ?> selected <?php endif; ?>>
                                 <?php echo e(app()->getLocale() == 'ar' ?$vend->Name :$vend->NameEn); ?>          
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for=""> <?php echo e(trans('admin.Vendor_And_Clients')); ?> </label>
                              <select class="select2 form-control w-100" name="V_and_C">
                                 <option value="1" <?php if($Purchases->V_and_C == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                                  
                                 <option value="0" <?php if($Purchases->V_and_C == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                                
                              </select>
                           </div>
                   
                            <div class="form-group col-md-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Empp')); ?></label>
                              <select class="select2 form-control w-100" name="Empp">
                                 <option value="1" <?php if($Purchases->Empp == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                                  
                                 <option value="0" <?php if($Purchases->Empp == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                                
                              </select>
                           </div>
                
                           <div class="form-group col-md-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Quality_Qty_Purch_Order')); ?></label>
                              <select class="select2 form-control w-100" name="Quality_Qty">
                                 <option value="1" <?php if($Purchases->Quality_Qty == 1): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Use')); ?> </option>
                                  
                                 <option value="0" <?php if($Purchases->Quality_Qty == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Not_Use')); ?> </option>
                                
                              </select>
                           </div>
                                  <div class="form-group col-md-3">
                              <label class="form-label" for=""> <?php echo e(trans('admin.Discount')); ?></label>
                              <select class="select2 form-control w-100" name="Discount">
                                 <option value="1" <?php if($Purchases->Discount == 1): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Precent')); ?> </option>
                                 <option value="0" <?php if($Purchases->Discount == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Number')); ?> </option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3" style="display: none">
                              <label class="form-label" for=""><?php echo e(trans('admin.Appear')); ?></label>
                              <?php echo e(trans('admin.Brand')); ?> 
                              <input type="checkbox" name="Brand" value="1" <?php if($Purchases->Brand == 1): ?> checked <?php endif; ?>>
                              <?php echo e(trans('admin.Group')); ?> 
                              <input type="checkbox" name="Group" value="1" <?php if($Purchases->Group == 1): ?> checked <?php endif; ?>>          
                              <?php echo e(trans('admin.English_Name')); ?> 
                              <input type="checkbox" name="English_Name" value="1" <?php if($Purchases->English_Name == 1): ?> checked <?php endif; ?>>
                              <?php echo e(trans('admin.Expire')); ?> 
                              <input type="checkbox" name="Expire" value="1" <?php if($Purchases->Expire == 1): ?> checked <?php endif; ?>>          
                           </div>
                           <div class="col-md-12">
                              <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Save')); ?></button>
                           </div>
                        </form>
                        <?php else: ?>
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="<?php echo e(url('AddDefaultPurchasesFirst')); ?>">
                                       <?php echo e(trans('admin.AddNew')); ?>

                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <?php endif; ?>              
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-4" role="tabpanel">
                        <?php if(!empty($Sales)): ?>  
                        <form action="<?php echo e(url('AddDefaultSales')); ?>" method="post" enctype="multipart/form-data" class="form-row">
                           <?php echo csrf_field(); ?>

                           <?php echo view('honeypot::honeypotFormFields'); ?>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Store')); ?></label>
                              <select class="select2 form-control w-100"  name="Store" required>
                                 <option value=""> <?php echo e(trans('admin.Store')); ?></option>
                                 <?php $__currentLoopData = $Storess; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($stor->id); ?>" <?php if($stor->id  == $Sales->Store): ?> selected <?php endif; ?>>
                            
                             <?php echo e(app()->getLocale() == 'ar' ?$stor->Name :$stor->NameEn); ?>         
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Safe')); ?></label>
                              <select class="select2 form-control w-100" name="Safe" required>
                                 <option value=""> <?php echo e(trans('admin.Safe')); ?></option>
                                 <?php $__currentLoopData = $Safes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $safe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($safe->id); ?>" <?php if($safe->id  == $Sales->Safe): ?> selected <?php endif; ?>>
                          
                          <?php echo e(app()->getLocale() == 'ar' ?$safe->Name :$safe->NameEn); ?>               
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                              </select>
                           </div>         
                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Bank')); ?></label>
                              <select class="select2 form-control w-100" name="Bank" required>
                                 <option value=""> <?php echo e(trans('admin.Bank')); ?></option>
                                 <?php $__currentLoopData = $Safes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $safe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                <?php if($safe->Parent  == 29): ?>  
                                 <option value="<?php echo e($safe->id); ?>" <?php if($safe->id  == $Sales->Bank): ?> selected <?php endif; ?>>
                          
                          <?php echo e(app()->getLocale() == 'ar' ?$safe->Name :$safe->NameEn); ?>               
                                 </option>
                                  <?php endif; ?>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                              <select class="select2 form-control w-100" name="Coin" required>
                                 <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                 <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($coin->id); ?>" <?php if($coin->id  == $Sales->Coin): ?> selected <?php endif; ?>>
                                   <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?> 
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label><?php echo e(trans('admin.Draw')); ?></label>  
                              <input type="number" step="any" name="Draw" class="form-control" value="<?php echo e($Sales->Draw); ?>" required>
                           </div>
                           <div class="form-group col-lg-3">
                              <label><?php echo e(trans('admin.Shift_Pass')); ?></label>  
                              <input type="number" step="any" name="Shift_Pass" class="form-control" value="<?php echo e($Sales->Shift_Pass); ?>" required>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""> <?php echo e(trans('admin.Payment_Method')); ?></label>
                              <select class="select2 form-control w-100" name="Payment_Method" id="Payment_Method"  required>
                              <option value="Cash" <?php if($Sales->Payment_Method == 'Cash'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Cash')); ?> </option>
                              <option value="Later" <?php if($Sales->Payment_Method == 'Later'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Later')); ?></option>
                              <option value="Check" <?php if($Sales->Payment_Method == 'Check'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Check')); ?></option>
                              <option value="Installment" <?php if($Sales->Payment_Method == 'Installment'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Installment')); ?></option> 
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Status')); ?></label>
                              <select class="select2 form-control w-100" name="Status"  id="Status"  required>
                              <option value="1" <?php if($Sales->Status == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Recived')); ?> </option>
                              <option value="0" <?php if( $Sales->Status == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Pending')); ?></option>
                              </select>
                           </div>
                                 <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.OtherStoresQty')); ?></label>
                              <select class="select2 form-control w-100" name="StoresQty"   required>
                              <option value="1" <?php if($Sales->StoresQty == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.All')); ?> </option>
                              <option value="0" <?php if( $Sales->StoresQty == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Choiced_Stores')); ?></option>
                              </select>
                           </div>
                            
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Delegate')); ?></label>
                              <select class="select2 form-control w-100" name="Delegate">
                                 <option value=""> <?php echo e(trans('admin.Delegate')); ?></option>
                                 <?php $__currentLoopData = $Employessss; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $emp): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($emp->id); ?>" <?php if($emp->id  == $Sales->Delegate): ?> selected <?php endif; ?>>
                      
                             <?php echo e(app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn); ?>         
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                              </select>
                           </div>
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Delivery')); ?></label>
                              <select class="select2 form-control w-100" name="Delivery">
                                 <option value=""> <?php echo e(trans('admin.Delivery')); ?></option>
                                 <?php $__currentLoopData = $Deliveries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $del): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($del->id); ?>" <?php if($del->id  == $Sales->Delivery): ?> selected <?php endif; ?>>
                     
                                    <?php echo e(app()->getLocale() == 'ar' ?$del->Name :$del->NameEn); ?>         
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                              </select>
                           </div> 
                            
                            
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Account')); ?></label>
                              <select class="select2 form-control w-100"  name="Client" required>
                                 <option value=""> <?php echo e(trans('admin.Account')); ?></option>
                                 <?php $__currentLoopData = $Vendors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vend): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($vend->id); ?>" <?php if($vend->id  == $Sales->Client): ?> selected <?php endif; ?>>
                  
                              <?php echo e(app()->getLocale() == 'ar' ?$vend->Name :$vend->NameEn); ?>           
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                              </select>
                           </div>
                            <div class="form-group col-md-3">
                              <label class="form-label" for=""> <?php echo e(trans('admin.Vendor_And_Clients')); ?> </label>
                              <select class="select2 form-control w-100" name="V_and_C">
                                 <option value="1" <?php if($Sales->V_and_C == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                                  
                                 <option value="0" <?php if($Sales->V_and_C == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                                
                              </select>
                           </div>
                     
                            <div class="form-group col-md-3">
                              <label class="form-label" for=""> <?php echo e(trans('admin.Discount')); ?></label>
                              <select class="select2 form-control w-100" name="Discount">
                                 <option value="1" <?php if($Sales->Discount == 1): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Precent')); ?> </option>
                                 <option value="0" <?php if($Sales->Discount == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Number')); ?> </option>
                              </select>
                           </div>
                    
                           <div class="form-group col-md-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Empp')); ?></label>
                              <select class="select2 form-control w-100" name="Empp">
                                 <option value="1" <?php if($Sales->Empp == 1): ?> selected <?php endif; ?>>  <?php echo e(trans('admin.Yes')); ?></option>
                                 <option value="0" <?php if($Sales->Empp == 0): ?> selected <?php endif; ?>> <?php echo e(trans('admin.No')); ?>  </option>
                              </select>
                           </div>
                    
                           
                           <div class="form-group col-lg-3" style="display: none">
                              <label class="form-label" for=""><?php echo e(trans('admin.Appear')); ?></label>
                              <?php echo e(trans('admin.Brand')); ?> 
                              <input type="checkbox" name="Brand" value="1" <?php if($Sales->Brand == 1): ?> checked <?php endif; ?>>
                              <?php echo e(trans('admin.Group')); ?> 
                              <input type="checkbox" name="Group" value="1" <?php if($Sales->Group == 1): ?> checked <?php endif; ?>>          
                              <?php echo e(trans('admin.English_Name')); ?> 
                              <input type="checkbox" name="English_Name" value="1" <?php if($Sales->English_Name == 1): ?> checked <?php endif; ?>>
                              <?php echo e(trans('admin.Expire')); ?> 
                              <input type="checkbox" name="Expire" value="1" <?php if($Sales->Expire == 1): ?> checked <?php endif; ?>>          
                           </div>
                            <div class="form-group col-md-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Price_Sale')); ?></label>
                              <select class="select2 form-control w-100" name="Price_Sale">
                                 <option value="1" <?php if($Sales->Price_Sale == 1): ?> selected <?php endif; ?>>  <?php echo e(trans('admin.Open')); ?></option>
                                 <option value="0" <?php if($Sales->Price_Sale == 0): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Disabled')); ?>  </option>
                              </select>
                           </div>
                            
                                    <div class="form-group col-md-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Execute_Precent')); ?></label>
                              <select class="select2 form-control w-100" name="Execute_Precent">
                                 <option value="Servicee" <?php if($Sales->Execute_Precent == 'Servicee'): ?> selected <?php endif; ?>>  <?php echo e(trans('admin.Servicee')); ?></option>
                                 <option value="Total_Cost" <?php if($Sales->Execute_Precent == 'Total_Cost'): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Total_Cost')); ?>  </option>
                              </select>
                           </div>
                            
                                           <div class="form-group col-md-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.DelegateEmp')); ?></label>
                              <select class="select2 form-control w-100" name="DelegateEmp">
                                 <option value="1" <?php if($Sales->DelegateEmp == 'Servicee'): ?> selected <?php endif; ?>>  <?php echo e(trans('admin.Show')); ?></option>
                                 <option value="0" <?php if($Sales->DelegateEmp == 'Total_Cost'): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Hide')); ?>  </option>
                              </select>
                           </div>
                            
                              <div class="form-group col-lg-3">
                           <label class="form-label" for=""><?php echo e(trans('admin.TaxBill')); ?></label>
                           <select class="select2 form-control w-100" name="TaxType" required>
                              <option value="0" <?php if($Sales->TaxType == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No_Tax')); ?></option>
                              <option value="1" <?php if($Sales->TaxType == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes_Tax')); ?></option>
                           </select>
                        </div>
                            
                            
                                       <div class="form-group col-lg-3">
                           <label class="form-label" for=""><?php echo e(trans('admin.DiscountTaxShow')); ?></label>
                           <select class="select2 form-control w-100" name="DiscountTaxShow" required>
                              <option value="0" <?php if($Sales->DiscountTaxShow == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                              <option value="1" <?php if($Sales->DiscountTaxShow == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                           </select>
                        </div>          
                            
                            
                            <div class="form-group col-lg-3">
                           <label class="form-label" for=""><?php echo e(trans('admin.SalesOrderType')); ?></label>
                           <select class="select2 form-control w-100" name="SalesOrderType" required>
                              <option value="0" <?php if($Sales->SalesOrderType == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Not_Hold_Qties')); ?></option>
                              <option value="1" <?php if($Sales->SalesOrderType == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hold_Qties')); ?></option>
                           </select>
                        </div>
                            
                            
                                        
                            <div class="form-group col-lg-3">
                           <label class="form-label" for=""><?php echo e(trans('admin.ECommercceSaleType')); ?></label>
                           <select class="select2 form-control w-100" name="ECommercceSaleType" required>
                              <option value="0" <?php if($Sales->ECommercceSaleType == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Not_Hold_Qties')); ?></option>
                              <option value="1" <?php if($Sales->ECommercceSaleType == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hold_Qties')); ?></option>
                           </select>
                                
                        </div>  
                            
                            
                            <div class="form-group col-lg-3">
                           <label class="form-label" for=""><?php echo e(trans('admin.Kitchen_Order')); ?></label>
                           <select class="select2 form-control w-100" name="Kitchen_Order" required>
                              <option value="0" <?php if($Sales->Kitchen_Order == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Screen')); ?></option>
                              <option value="1" <?php if($Sales->Kitchen_Order == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Recipt')); ?></option>
                              <option value="2" <?php if($Sales->Kitchen_Order == 2): ?> selected <?php endif; ?>><?php echo e(trans('admin.Both')); ?></option>
                           </select>
                        </div>
                            
                            
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Waiter')); ?></label>
                              <select class="select2 form-control w-100" name="Waiter" required>
                                 <option value=""> <?php echo e(trans('admin.Waiter')); ?></option>
                                 <?php $__currentLoopData = $Waiters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $wait): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($wait->id); ?>" <?php if($wait->id  == $Sales->Waiter): ?> selected <?php endif; ?>>
                      
                             <?php echo e(app()->getLocale() == 'ar' ?$wait->Name :$wait->NameEn); ?>         
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                              </select>
                           </div>         
                            
                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.CoinResturantWebsite')); ?></label>
                              <select class="select2 form-control w-100" name="CountryResturantWebsite" required>
                                 <option value=""> <?php echo e(trans('admin.CoinResturantWebsite')); ?></option>
                                 <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($coin->id); ?>" <?php if($coin->id  == $Sales->CountryResturantWebsite): ?> selected <?php endif; ?>>
                      
                             <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?>         
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                              </select>
                           </div>
                            
                            
                                     <div class="form-group col-lg-3">
                           <label class="form-label" for=""><?php echo e(trans('admin.Hall_Service_Type')); ?></label>
                           <select class="select2 form-control w-100" name="Hall_Service_Type" id="Hall_Service_Type" required onchange="HallService()">
                              <option value="0" <?php if($Sales->Hall_Service_Type == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                              <option value="1" <?php if($Sales->Hall_Service_Type == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                           </select>
                        </div>                    
                            
                            <div class="form-group col-lg-3">
                           <label class="form-label" for=""><?php echo e(trans('admin.SalesLowCostPrice')); ?></label>
                           <select class="select2 form-control w-100" name="SalesLowCostPrice">
                              <option value="0" <?php if($Sales->SalesLowCostPrice == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                              <option value="1" <?php if($Sales->SalesLowCostPrice == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                           </select>
                        </div>       
                            
                            
                            <div class="form-group col-lg-3">
                           <label class="form-label" for=""><?php echo e(trans('admin.ShowJobOrders')); ?></label>
                           <select class="select2 form-control w-100" name="ShowJobOrders">
                            
                              <option value="1" <?php if($Sales->ShowJobOrders == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?></option>
                                 <option value="0" <?php if($Sales->ShowJobOrders == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                           </select>
                        </div>
                            
                            
                              
                                     <div class="form-group col-lg-3" id="Hall_Service_Precent" style="display: none">
                           <label class="form-label" for=""><?php echo e(trans('admin.Hall_Service_Precent')); ?></label>
                                  <input type="number" step="any" class="form-control" name="Hall_Service_Precent" value="<?php echo e($Sales->Hall_Service_Precent); ?>" >       
                            </div> 
                            
                                                                 <div class="form-group col-md-3">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Country')); ?> </label>
                              <select class="select2 form-control w-100"  name="Country" required>
                                 <option value=""><?php echo e(trans('admin.Country')); ?></option>
                                 <?php $__currentLoopData = $Nationality; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $nation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <option value="<?php echo e($nation->id); ?>"  <?php if($nation->id  == $Sales->Country): ?> selected <?php endif; ?>>

                                   <?php echo e(app()->getLocale() == 'ar' ?$nation->Arabic_Name :$nation->English_Name); ?>      
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>      
                              </select>
                           </div>
                            

                            
                                          <div class="form-group col-lg-3">
                           <label class="form-label" for=""><?php echo e(trans('admin.Duplicate_Items')); ?></label>
                           <select class="select2 form-control w-100" name="Duplicate_Items">
                              <option value="0" <?php if($Sales->Duplicate_Items == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                              <option value="1" <?php if($Sales->Duplicate_Items == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                           </select>
                        </div>       
                            
                            
                                          <div class="form-group col-lg-3">
                           <label class="form-label" for=""><?php echo e(trans('admin.LimitSalesQty')); ?></label>
                           <select class="select2 form-control w-100" name="LimitSalesQty">
                              <option value="0" <?php if($Sales->LimitSalesQty == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                              <option value="1" <?php if($Sales->LimitSalesQty == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                           </select>
                        </div>       
                            
                            
                            <div class="form-group col-lg-3">
                           <label class="form-label" for=""><?php echo e(trans('admin.Total_Wight_Bill')); ?></label>
                           <select class="select2 form-control w-100" name="Total_Wight_Bill">
                            
                              <option value="1" <?php if($Sales->Total_Wight_Bill == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?></option>
                                 <option value="0" <?php if($Sales->Total_Wight_Bill == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                           </select>
                        </div>
                            
                            
                            
                            


                            
                        
                           <input type="hidden" name="Mainus" value="0">   
                           <!--   <div class="form-group col-lg-3" >
                              <label class="form-label" for=""><?php echo e(trans('admin.Mainus')); ?></label>
                              <input type="radio" name="Mainus" value="0" checked> 
                               <?php echo e(trans('admin.Yes')); ?> 
                              <input type="radio" name="Mainus" value="1" <?php if($Sales->Mainus == 1): ?> checked <?php endif; ?>>
                                
                               <?php echo e(trans('admin.No')); ?> 
                              <input type="radio" name="Mainus" value="0" <?php if($Sales->Mainus == 0): ?> checked <?php endif; ?>>          
                                
                              </div> -->
                           <div class="col-md-12">
                              <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Save')); ?></button>
                           </div>
                        </form>
                        <?php else: ?>
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="<?php echo e(url('AddDefaultSalesFirst')); ?>">
                                       <?php echo e(trans('admin.AddNew')); ?>

                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <?php endif; ?>                   
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-5" role="tabpanel">
                        <?php if(!empty($Crms)): ?>  
                        <form action="<?php echo e(url('AddDefaultCrm')); ?>" method="post"  class="form-row">
                           <?php echo csrf_field(); ?>  
                           <?php echo view('honeypot::honeypotFormFields'); ?>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">   <?php echo e(trans('admin.Price_Level')); ?> </label><span class="strick">*</span>
                              <select class="select2 form-control w-100" name="Price_Level" required>
                                 <option value=""> <?php echo e(trans('admin.Price_Level')); ?> </option>
                                 <option value="1"  <?php if($Crms->Price_Level == 1): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Level1')); ?> </option>
                                 <option value="2"  <?php if($Crms->Price_Level == 2): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Level2')); ?> </option>
                                 <option value="3"  <?php if($Crms->Price_Level == 3): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Level3')); ?> </option>
                              </select>
                           </div>
                                <div class="form-group col-md-3">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Nationality')); ?> </label>
                              <select class="select2 form-control w-100"  name="Nationality" required>
                                 <option value=""><?php echo e(trans('admin.Nationality')); ?></option>
                                 <?php $__currentLoopData = $Nationality; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $nation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <option value="<?php echo e($nation->id); ?>"  <?php if($nation->id  == $Crms->Nationality): ?> selected <?php endif; ?>>

                                   <?php echo e(app()->getLocale() == 'ar' ?$nation->Arabic_Name :$nation->English_Name); ?>      
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>      
                              </select>
                           </div>          
                            <div class="form-group col-md-3">
                              <label class="form-label" for="">  <?php echo e(trans('admin.ClientGroup')); ?> </label>
                              <select class="select2 form-control w-100"  name="ClientGroup" required>
                                 <option value=""><?php echo e(trans('admin.ClientGroup')); ?></option>
                                 <?php $__currentLoopData = $CustomersGroup; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $grop): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <option value="<?php echo e($grop->id); ?>"  <?php if($grop->id  == $Crms->ClientGroup): ?> selected <?php endif; ?>>

                                   <?php echo e(app()->getLocale() == 'ar' ?$grop->Arabic_Name :$grop->English_Name); ?>      
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>      
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Governrate')); ?> </label>
                              <select class="select2 form-control w-100" id="Governrate" name="Governrate" required>
                                 <option value=""><?php echo e(trans('admin.Governrate')); ?></option>
                                 <?php $__currentLoopData = $Governrates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gov): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <option value="<?php echo e($gov->id); ?>"  <?php if($gov->id  == $Crms->Governrate): ?> selected <?php endif; ?>>  
                             <?php echo e(app()->getLocale() == 'ar' ?$gov->Arabic_Name :$gov->English_Name); ?>         
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>      
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  <?php echo e(trans('admin.City')); ?></label>
                              <select class="select2 form-control w-100" id="City" name="City" required>
                                 <?php if(!empty($Crms->City)): ?>     
                                 <option value="<?php echo e($Crms->City); ?>">
                                  
                                           <?php echo e(app()->getLocale() == 'ar' ?$Crms->City()->first()->Arabic_Name :$Crms->City()->first()->English_Name); ?>   
                                  </option>
                                 <?php endif; ?>   
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">   <?php echo e(trans('admin.ClientStatus')); ?></label>
                              <select class="select2 form-control w-100" name="ClientStatus" required>
                                 <option value=""><?php echo e(trans('admin.ClientStatus')); ?></option>
                                 <?php $__currentLoopData = $ClientStatus; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cls): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <option value="<?php echo e($cls->id); ?>"  <?php if($cls->id  == $Crms->ClientStatus): ?> selected <?php endif; ?>>  
                                 <?php echo e(app()->getLocale() == 'ar' ?$cls->Arabic_Name :$cls->English_Name); ?>             
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>      
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Platforms')); ?> </label>
                              <select class="select2 form-control w-100" id="Platform" name="Platforms" required>
                                 <option value=""><?php echo e(trans('admin.Platforms')); ?></option>
                                 <?php $__currentLoopData = $Platforms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pl): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <option value="<?php echo e($pl->id); ?>"  <?php if($pl->id  == $Crms->Platforms): ?> selected <?php endif; ?>>

                                         <?php echo e(app()->getLocale() == 'ar' ?$pl->Arabic_Name :$pl->English_Name); ?>       
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>   
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Campagin')); ?> </label>
                              <select class="select2 form-control w-100" id="Campagin" name="Campagin" required>
                                 <?php if(!empty($Crms->Campagin)): ?>     
                                 <option value="<?php echo e($Crms->Campagin); ?>">
                                  
                                       <?php echo e(app()->getLocale() == 'ar' ?$Crms->Campagin()->first()->Arabic_Name :$Crms->Campagin()->first()->English_Name); ?>       
                                  </option>
                                 <?php endif; ?>   
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Activity')); ?></label>
                              <select class="select2 form-control w-100" name="Activity" required>
                                 <option value=""><?php echo e(trans('admin.Activity')); ?></option>
                                 <?php $__currentLoopData = $Activites; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $act): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <option value="<?php echo e($act->id); ?>"  <?php if($act->id  == $Crms->Activity): ?> selected <?php endif; ?>> 
                               <?php echo e(app()->getLocale() == 'ar' ?$act->Arabic_Name :$act->English_Name); ?>             
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Responsible')); ?></label>
                              <select class="select2 form-control w-100" name="Responsible" required>
                                 <option value=""><?php echo e(trans('admin.Responsible')); ?></option>
                                 <?php $__currentLoopData = $Employess; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $emp): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <option value="<?php echo e($emp->id); ?>"  <?php if($emp->id  == $Crms->Responsible): ?> selected <?php endif; ?>>
                 
                             <?php echo e(app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn); ?>         
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>   
                              </select>
                           </div>
                           <div class="form-group col-md-3">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Client_Delegate')); ?></label>
                              <select class="select2 form-control w-100" name="Client_Delegate" required>
                                 <option value=""><?php echo e(trans('admin.Client_Delegate')); ?></option>
                                 <option value="1" <?php if($Crms->Client_Delegate == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?></option>
                                 <option value="0" <?php if($Crms->Client_Delegate == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                              </select>
                           </div>
                           <div class="col-md-12">
                              <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Save')); ?></button>
                           </div>
                        </form>
                        <?php else: ?>
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="<?php echo e(url('AddDefaultCrmFirst')); ?>">
                                       <?php echo e(trans('admin.AddNew')); ?>

                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <?php endif; ?>        
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-8" role="tabpanel">
                        <?php if(!empty($Companies)): ?>            
                        <form action="<?php echo e(url('AddDefaultCompany')); ?>" method="post" enctype="multipart/form-data" class="form-row">
                           <?php echo csrf_field(); ?>

                           <?php echo view('honeypot::honeypotFormFields'); ?>
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Company_Arabic_Name')); ?>   </label>
                                       <input type="text" name="Name" value="<?php echo e($Companies->Name); ?>" class="form-control" required>
                                    </div>
                                     
                                                 <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Company_English_Name')); ?>   </label>
                                       <input type="text" name="NameEn" value="<?php echo e($Companies->NameEn); ?>" class="form-control" required>
                                    </div>            
                                     
                                     
                                     <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Email')); ?>   </label>
                                       <input type="email" name="Email" value="<?php echo e($Companies->Email); ?>" class="form-control" required>
                                    </div>
                                     
                                    <div class="form-group col-md-3">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Phone')); ?>   </label>
                                       <input type="number" name="Phone1" value="<?php echo e($Companies->Phone1); ?>" class="form-control">
                                    </div>
                                    <div class="form-group col-md-3">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Phone2')); ?>   </label>
                                       <input type="number" name="Phone2" value="<?php echo e($Companies->Phone2); ?>" class="form-control">
                                    </div>
                                           <div class="form-group col-md-3">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Phone3')); ?>   </label>
                                       <input type="number" name="Phone3" value="<?php echo e($Companies->Phone3); ?>" class="form-control">
                                    </div>
                                    <div class="form-group col-md-3">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Phone4')); ?>   </label>
                                       <input type="number" name="Phone4" value="<?php echo e($Companies->Phone4); ?>" class="form-control">
                                    </div>
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Company_Address_Arabic')); ?>   </label>
                                       <input type="text" name="Address" value="<?php echo e($Companies->Address); ?>" class="form-control">
                                    </div>      
                                     
                                     <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Company_Address_English')); ?>   </label>
                                       <input type="text" name="AddressEn" value="<?php echo e($Companies->AddressEn); ?>" class="form-control">
                                    </div>
                                  
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for="">    <?php echo e(trans('admin.Logo')); ?>    </label>
                                       <input type="file" name="Logo">
                                    </div>
                                    <div class="form-group col-md-6">
                                       <img class="img-fluid" src="<?php echo e(URL::to($Companies->Logo)); ?>">  
                                       <input type="hidden" name="Logos" value="<?php echo e($Companies->Logo); ?>">                   
                                    </div>
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for="">    <?php echo e(trans('admin.Icon')); ?>    </label>
                                       <input type="file" name="Icon">
                                    </div>
                                    <div class="form-group col-md-6">
                                       <img class="img-fluid" src="<?php echo e(URL::to($Companies->Icon)); ?>">  
                                       <input type="hidden" name="Icons" value="<?php echo e($Companies->Icon); ?>">                
                                    </div>
                                           <div class="form-group col-md-6">
                                       <label class="form-label" for="">    <?php echo e(trans('admin.Logo_Store')); ?>    </label>
                                       <input type="file" name="Logo_Store">
                                    </div>
                                     
                                    <div class="form-group col-md-6">
                                       <img class="img-fluid" src="<?php echo e(URL::to($Companies->Logo_Store)); ?>">  
                                       <input type="hidden" name="Logo_StoreS" value="<?php echo e($Companies->Logo_Store); ?>">
                                    </div> 
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for="">    <?php echo e(trans('admin.Icon_Store')); ?>    </label>
                                       <input type="file" name="Icon_Store">
                                    </div>
                                    <div class="form-group col-md-6">
                                       <img class="img-fluid" src="<?php echo e(URL::to($Companies->Icon_Store)); ?>">  
                                       <input type="hidden" name="Icon_StoreS" value="<?php echo e($Companies->Icon_Store); ?>">
                                    </div>  
                                     
                                     
                                         <?php if($Modules->Resturant  ==  1): ?>
                                               <div class="form-group col-md-6">
                                       <label class="form-label" for="">    PDF Menu    </label>
                                       <input type="file" name="PDF">
                                    </div>
                                    <div class="form-group col-md-6">
                                   <a class="btn btn-primary" download href="<?php echo e(URL::to($Companies->PDF)); ?>"><i class="fal fa-download"></i></a>
                                       <input type="hidden" name="PDFs" value="<?php echo e($Companies->PDF); ?>">
                                    </div>  
                                     <?php endif; ?>
                                     
                                     
                                                 <div class="form-group col-md-6">
                                       <label class="form-label" for="">    <?php echo e(trans('admin.Location')); ?>    </label>
                                       <input type="text" name="Location" class="form-control" required value="<?php echo e($Companies->Location); ?>">
                                    </div>
                                    <div class="form-group col-md-6">
                                      <?php echo $Companies->Location; ?>

                                    </div>  
                                     
                                           <div class="form-group col-md-12">
                                       <label class="form-label" for="">    <?php echo e(trans('admin.Font_Type')); ?>    </label>
                              <select class="select2 form-control" name="Font_Type" required>
                    <option value="1" <?php if($Companies->Font_Type == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Font_1')); ?></option>    
                  <option value="2" <?php if($Companies->Font_Type  == 2): ?> selected <?php endif; ?>><?php echo e(trans('admin.Font_2')); ?></option>  
                  <option value="3" <?php if($Companies->Font_Type  == 3): ?> selected <?php endif; ?>><?php echo e(trans('admin.Font_3')); ?></option>  
                  <option value="4" <?php if($Companies->Font_Type  == 4): ?> selected <?php endif; ?>><?php echo e(trans('admin.Font_4')); ?></option>  
                  <option value="5" <?php if($Companies->Font_Type  == 5): ?> selected <?php endif; ?>><?php echo e(trans('admin.Font_5')); ?></option>  
                              </select>
                                    </div>     
                                     
                                     


                                     
                                     
                                     
                                     <?php if(auth()->guard('admin')->user()->email == '<EMAIL>'): ?>
                                    <div class="form-group col-md-4">
                                       <label class="form-label" for="">    <?php echo e(trans('admin.View')); ?>    </label>
                              <select class="select2 form-control" name="View" required>
                    <option value="0" <?php if($Companies->View == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Default')); ?></option>    
                  <option value="1" <?php if($Companies->View  == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.ECommerce')); ?></option>  
                  <option value="2" <?php if($Companies->View  == 2): ?> selected <?php endif; ?>><?php echo e(trans('admin.ResturantMenu')); ?></option>  
                              </select>
                                    </div>
                                                       <div class="form-group col-md-4">
                                       <label class="form-label" for="">    <?php echo e(trans('admin.DB_Backup')); ?>    </label>
                              <select class="select2 form-control" name="DB_Backup" required>
                    <option value="0" <?php if($Companies->DB_Backup == 0): ?> selected <?php endif; ?>>Local</option>    
                  <option value="1" <?php if($Companies->DB_Backup  == 1): ?> selected <?php endif; ?>>Web</option>  
                              </select>
                                    </div>                 
                                     
                                     <div class="form-group col-md-4">
                                       <label class="form-label" for="">    <?php echo e(trans('admin.HomeMainScreen')); ?>    </label>
                              <select class="select2 form-control" name="HomeMainScreen" required>
                    <option value="0" <?php if($Companies->HomeMainScreen == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Briefs')); ?></option>    
                  <option value="1" <?php if($Companies->HomeMainScreen  == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Reports')); ?></option>  
                              </select>
                                    </div>
                                     
                                     
                                                  <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Welcome_Arabic_Word_App')); ?>   </label>
                                       <input type="text" name="Welcome_Arabic_Word_App" value="<?php echo e($Companies->Welcome_Arabic_Word_App); ?>" class="form-control">
                                    </div> 
                                               
                                                  <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Welcome_English_Word_App')); ?>   </label>
                                       <input type="text" name="Welcome_English_Word_App" value="<?php echo e($Companies->Welcome_English_Word_App); ?>" class="form-control">
                                    </div> 
                                     

                                     <?php else: ?>
                                     
                                     <input type="hidden" name="View" value="<?php echo e($Companies->View); ?>">
                                     <input type="hidden" name="DB_Backup" value="<?php echo e($Companies->DB_Backup); ?>">
                                     <input type="hidden" name="HomeMainScreen" value="<?php echo e($Companies->HomeMainScreen); ?>">
                                     <input type="hidden" name="Welcome_Arabic_Word_App" value="<?php echo e($Companies->Welcome_Arabic_Word_App); ?>">
                                     <input type="hidden" name="Welcome_English_Word_App" value="<?php echo e($Companies->Welcome_English_Word_App); ?>">
                                     <?php endif; ?>
                                     
                               
                                 </div>
                                  
                                  <div class="row m-2">
          <button style="width: 100%;    background: linear-gradient(1deg, #7d0000, black);font-size: 30px;" class="btn btn-primary" type="button" data-toggle="collapse" data-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
   <?php echo e(trans('admin.Print')); ?>

  </button>
                                      
                        </div>              
                                  
              <div class="row collapse" id="collapseExample" style="background: linear-gradient(0deg, #510000, #000000);color: #ffd806;padding: 35px;border-radius: 6%;"> 
                                     
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Print_Text')); ?>   </label>
                                       <input type="text" name="Print_Text" value="<?php echo e($Companies->Print_Text); ?>" class="form-control" required>
                                    </div>
                                     
                         <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.English_Print_Text')); ?>   </label>
                                       <input type="text" name="Print_Text_En" value="<?php echo e($Companies->Print_Text_En); ?>" class="form-control" required>
                                    </div>
                  
                  
                                    <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Print_Text_Footer')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       <?php echo e($Companies->Print_Text_Footer); ?>          
                                       </textarea>
                                    </div>
                                     
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Print_Text_Footer')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer">
                                       <?php echo e($Companies->Print_Text_Footer); ?>          
                                       </textarea>
                                    </div>
                                     
                  
                    <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.English_Print_Text_Footer')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       <?php echo e($Companies->Print_Text_Footer_En); ?>          
                                       </textarea>
                                    </div>
                                     
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.English_Print_Text_Footer')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer_En">
                                       <?php echo e($Companies->Print_Text_Footer_En); ?>          
                                       </textarea>
                                    </div>
                  

                                      <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Print_Text_Footer_Manufacturing')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer_Manufacturing">
                                       <?php echo e($Companies->Print_Text_Footer_Manufacturing); ?>          
                                       </textarea>
                                    </div>
                                    <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Print_Text_Footer_Manufacturing')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       <?php echo e($Companies->Print_Text_Footer_Manufacturing); ?>          
                                       </textarea>
                                    </div>
                  
                  
                              <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.English_Print_Text_Footer_Manufacturing')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer_Manufacturing_En">
                                       <?php echo e($Companies->Print_Text_Footer_Manufacturing_En); ?>          
                                       </textarea>
                                    </div>
                                    <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.English_Print_Text_Footer_Manufacturing')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       <?php echo e($Companies->Print_Text_Footer_Manufacturing_En); ?>          
                                       </textarea>
                                    </div>
                  
   
                             <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Print_Text_Footer_Sales')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       <?php echo e($Companies->Print_Text_Footer_Sales); ?>          
                                       </textarea>
                                    </div>
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Print_Text_Footer_Sales')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer_Sales">
                                       <?php echo e($Companies->Print_Text_Footer_Sales); ?>          
                                       </textarea>
                                    </div>
                  
                  
                  
                       <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.English_Print_Text_Footer_Sales')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       <?php echo e($Companies->Print_Text_Footer_Sales); ?>          
                                       </textarea>
                                    </div>
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.English_Print_Text_Footer_Sales')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer_Sales_En">
                                       <?php echo e($Companies->Print_Text_Footer_Sales_En); ?>          
                                       </textarea>
                                    </div>
                  

                  
                          <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Print_Text_Footer_Quote')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       <?php echo e($Companies->Print_Text_Footer_Quote); ?>          
                                       </textarea>
                                    </div>
                  
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Print_Text_Footer_Quote')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer_Quote">
                                       <?php echo e($Companies->Print_Text_Footer_Quote); ?>          
                                       </textarea>
                                    </div>
                  
                  
                  
                         <div class="form-group col-md-6" style="display: none">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.English_Print_Text_Footer_Quote')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="">
                                       <?php echo e($Companies->Print_Text_Footer_Quote_En); ?>          
                                       </textarea>
                                    </div>
                  
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.English_Print_Text_Footer_Quote')); ?>   </label>
                                       <textarea class="js-summernote" id="saveToLocal" name="Print_Text_Footer_Quote_En">
                                       <?php echo e($Companies->Print_Text_Footer_Quote_En); ?>          
                                       </textarea>
                                    </div>
                  
             
                  
                  
                  
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Print_Text_Footer_Secretariat')); ?>   </label>
                                       <input type="text" name="Print_Text_Footer_Secretariat" value="<?php echo e($Companies->Print_Text_Footer_Secretariat); ?>" class="form-control">
                                    </div>      
                  
                                 <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.English_Print_Text_Footer_Secretariat')); ?>   </label>
                                       <input type="text" name="Print_Text_Footer_Secretariat_En" value="<?php echo e($Companies->Print_Text_Footer_Secretariat_En); ?>" class="form-control">
                                    </div>
                  
                  
                  
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Name_Sales_Bill')); ?>   </label>
                                       <input type="text" name="Name_Sales_Bill" value="<?php echo e($Companies->Name_Sales_Bill); ?>" class="form-control">
                                    </div>     
                  
                  <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.English_Name_Sales_Bill')); ?>   </label>
                                       <input type="text" name="Name_Sales_Bill_En" value="<?php echo e($Companies->Name_Sales_Bill_En); ?>" class="form-control">
                                    </div>
                  
                  
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Name_Sales_Order_Bill')); ?>   </label>
                                       <input type="text" name="Name_Sales_Order_Bill" value="<?php echo e($Companies->Name_Sales_Order_Bill); ?>" class="form-control">
                                    </div>    
                  
                  <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.English_Name_Sales_Order_Bill')); ?>   </label>
                                       <input type="text" name="Name_Sales_Order_Bill_En" value="<?php echo e($Companies->Name_Sales_Order_Bill_En); ?>" class="form-control">
                                    </div>
                  
                  
                                    <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Arabic_Name_Quote_Bill')); ?>   </label>
                                       <input type="text" name="Name_Quote_Bill" value="<?php echo e($Companies->Name_Quote_Bill); ?>" class="form-control">
                                    </div>    
                  
                  <div class="form-group col-md-6">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.English_Name_Quote_Bill')); ?>   </label>
                                       <input type="text" name="Name_Quote_Bill_En" value="<?php echo e($Companies->Name_Quote_Bill_En); ?>" class="form-control">
                                    </div>
                  
                  
                  
                  
                                <div class="form-group col-md-12">
                                       <label class="form-label" for="">    <?php echo e(trans('admin.Bill_View')); ?> (<?php echo e(trans('admin.Sales')); ?>)    </label>
                             <select class="select2 form-control" name="Bill_View" required>
                                <option value="1" <?php if($Companies->Bill_View == 1): ?> selected <?php endif; ?>>View 1</option>    
                                <option value="2" <?php if($Companies->Bill_View == 2): ?> selected <?php endif; ?>>View 2</option>    
                            
                                    
                                    </select>              
                                    </div>
                  
                  
                                    <div class="form-group col-md-12">
                                       <label class="form-label" for="">    <?php echo e(trans('admin.Seal')); ?>    </label>
                                       <input type="file" name="Seal">
                                       <img class="img-fluid" src="<?php echo e(URL::to($Companies->Seal)); ?>">  
                                       <input type="hidden" name="Seals" value="<?php echo e($Companies->Seal); ?>">                
                                    </div>
    
                                        </div>
 
                                  
                                   <div class="row m-2">
          <button style="width: 100%;    background: linear-gradient(1deg, #f1e1f0, #00000061);color: black;font-size: 30px;" class="btn btn-primary" type="button" data-toggle="collapse" data-target="#collapseExample1" aria-expanded="false" aria-controls="collapseExample">
   <?php echo e(trans('admin.Commercial')); ?>

  </button>
                                      
                        </div>                              
                                  
                                  
               <div class="row collapse" id="collapseExample1" style="border: 1px solid #ccc;border-radius: 5px;background: #8000801f;padding: 10px;margin: 5px;">
                                     
                                      <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Commercial_Record')); ?>   </label>
                                       <input type="text" name="Commercial_Record" value="<?php echo e($Companies->Commercial_Record); ?>" class="form-control">
                                    </div>
                                    
                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Tax_File_Number')); ?>   </label>
                                       <input type="text" name="Tax_File_Number" value="<?php echo e($Companies->Tax_File_Number); ?>" class="form-control">
                                    </div>
                                               
                                                <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Tax_Registration_Number')); ?>   </label>
                                       <input type="text" name="Tax_Registration_Number" value="<?php echo e($Companies->Tax_Registration_Number); ?>" class="form-control">
                                    </div>      
                                               
                                               
                                                     <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Tax_activity_code')); ?>   </label>
                                       <input type="text" name="Tax_activity_code" value="<?php echo e($Companies->Tax_activity_code); ?>" class="form-control">
                                    </div>   
                                               
                                           <div class="form-group col-md-4">
                                       <label class="form-label" for="">    <?php echo e(trans('admin.work_nature')); ?>    </label>
                              <select class="select2 form-control" name="work_nature" required>
                    <option value="P" <?php if($Companies->work_nature == 'P'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Person')); ?></option>    
                  <option value="B" <?php if($Companies->work_nature  == 'B'): ?> selected <?php endif; ?>><?php echo e(trans('admin.Egyptian_trading_company')); ?></option>  
                  <option value="F" <?php if($Companies->work_nature  == 'F'): ?> selected <?php endif; ?>><?php echo e(trans('admin.foreign_trading_company')); ?></option>  
                              </select>
                                    </div>            
                                     
                                     
                     <div class="form-group col-md-4">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Governrate')); ?> </label>
                              <select class="select2 form-control w-100 Governrate"  name="Governrate" required>
                                 <option value=""><?php echo e(trans('admin.Governrate')); ?></option>
                                 <?php $__currentLoopData = $Governrates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gov): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <option value="<?php echo e($gov->id); ?>"  <?php if($gov->id  == $Companies->Governrate): ?> selected <?php endif; ?>>
                  
                               <?php echo e(app()->getLocale() == 'ar' ?$gov->Arabic_Name :$gov->English_Name); ?>          
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>      
                              </select>
                           </div>
                           <div class="form-group col-md-4">
                              <label class="form-label" for="">  <?php echo e(trans('admin.City')); ?></label>
                              <select class="select2 form-control w-100 City"  name="City" required>
                                 <?php if(!empty($Companies->City)): ?>     
                                 <option value="<?php echo e($Companies->City); ?>">
                                  
                           <?php echo e(app()->getLocale() == 'ar' ?$Companies->City()->first()->Arabic_Name :$Companies->City()->first()->English_Name); ?>             
                                  </option>
                                 <?php endif; ?>   
                              </select>
                           </div>  
                                               
                                <div class="form-group col-md-4">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Place')); ?></label>
                              <select class="select2 form-control w-100 Place"  name="Place" required>
                                 <?php if(!empty($Companies->Place)): ?>     
                                 <option value="<?php echo e($Companies->Place); ?>">
                                  
                                      <?php echo e(app()->getLocale() == 'ar' ?$Companies->Place()->first()->Arabic_Name :$Companies->Place()->first()->English_Name); ?>  
                                  </option>
                                 <?php endif; ?>   
                              </select>
                           </div> 
                                                    <div class="form-group col-md-4">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Nationality')); ?> </label>
                              <select class="select2 form-control w-100"  name="Nationality" required>
                                 <option value=""><?php echo e(trans('admin.Nationality')); ?></option>
                                 <?php $__currentLoopData = $Nationality; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $nation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <option value="<?php echo e($nation->id); ?>"  <?php if($nation->id  == $Companies->Nationality): ?> selected <?php endif; ?>>

                                   <?php echo e(app()->getLocale() == 'ar' ?$nation->Arabic_Name :$nation->English_Name); ?>      
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>      
                              </select>
                           </div>


                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Buliding_Num')); ?>   </label>
                                       <input type="text" name="Buliding_Num" value="<?php echo e($Companies->Buliding_Num); ?>" class="form-control">
                                    </div> 
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Street')); ?>   </label>
                                       <input type="text" name="Street" value="<?php echo e($Companies->Street); ?>" class="form-control">
                                    </div> 
                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Postal_Code')); ?>   </label>
                                       <input type="text" name="Postal_Code" value="<?php echo e($Companies->Postal_Code); ?>" class="form-control">
                                    </div> 
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.tax_magistrate')); ?>   </label>
                                       <input type="text" name="tax_magistrate" value="<?php echo e($Companies->tax_magistrate); ?>" class="form-control">
                                    </div> 
                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> Client ID  </label>
                                       <input type="text" name="Client_ID" value="<?php echo e($Companies->Client_ID); ?>" class="form-control">
                                    </div> 
                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> Secret ID  </label>
                                       <input type="text" name="Serial_Client_ID" value="<?php echo e($Companies->Serial_Client_ID); ?>" class="form-control">
                                    </div> 
                                               
                                                                 <div class="form-group col-md-4">
                                       <label class="form-label" for="">    <?php echo e(trans('admin.Version_Type')); ?>    </label>
                              <select class="select2 form-control" name="Version_Type" required>
                    <option value=""><?php echo e(trans('admin.Version_Type')); ?></option>    
                  <option value="0.9" <?php if($Companies->Version_Type  == 0.9): ?> selected <?php endif; ?>>0.9</option>  
                  <option value="1.0" <?php if($Companies->Version_Type  == 1.0): ?> selected <?php endif; ?>>1.0</option>  
                              </select>
                                    </div> 
                                               
                                                                                             <div class="form-group col-md-4">
                                       <label class="form-label" for="">    <?php echo e(trans('admin.Invoice_Type')); ?>    </label>
                              <select class="select2 form-control" name="Invoice_Type" required>
                    <option value=""><?php echo e(trans('admin.Invoice_Type')); ?></option>    
                  <option value="Real" <?php if($Companies->Invoice_Type  == 'Real'): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Real')); ?> </option>  
                  <option value="Experimental" <?php if($Companies->Invoice_Type  == 'Experimental'): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Experimental')); ?> </option>  
                              </select>
                                    </div> 
                                               
                                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Computer_SN')); ?>   </label>
                                       <input type="text" name="Computer_SN" value="<?php echo e($Companies->Computer_SN); ?>" class="form-control" required>
                                    </div> 
                                          
                                               
                                                                                  <div class="form-group col-md-4">
                                       <label class="form-label" for=""> POS Version   </label>
                                       <input type="text" name="POS_Version" value="<?php echo e($Companies->POS_Version); ?>" class="form-control" required>
                                    </div> 
                                               
                                               
                                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Floor')); ?>   </label>
                                       <input type="text" name="Floor" value="<?php echo e($Companies->Floor); ?>" class="form-control">
                                    </div> 
                                               
                                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Room')); ?>   </label>
                                       <input type="text" name="Room" value="<?php echo e($Companies->Room); ?>" class="form-control">
                                    </div> 
                                               
                                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Landmark')); ?>   </label>
                                       <input type="text" name="Landmark" value="<?php echo e($Companies->Landmark); ?>" class="form-control">
                                    </div> 
                                               
                                                               
                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Add_Info')); ?>   </label>
                                       <input type="text" name="Add_Info" value="<?php echo e($Companies->Add_Info); ?>" class="form-control">
                                    </div> 
                                           
                                               
                                                                                    <div class="form-group col-md-4">
                                       <label class="form-label" for=""> <?php echo e(trans('admin.Path')); ?>   </label>
                                       <input type="text" name="Path" value="<?php echo e($Companies->Path); ?>" class="form-control">
                                    </div> 
                                               
                                               
                                         
                                     </div> 
                              </div>
                           </div>
                           <div class="col-md-12">
                              <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Save')); ?></button>
                           </div>
                        </form>
                        <?php else: ?>
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="<?php echo e(url('AddDefaultCompanyFirst')); ?>">
                                       <?php echo e(trans('admin.AddNew')); ?>

                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <?php endif; ?>            
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-10" role="tabpanel">
                        <?php if(!empty($Maint)): ?>  
                        <form action="<?php echo e(url('AddDefaultMaintaince')); ?>" method="post"  class="form-row">
                           <?php echo csrf_field(); ?>  
                           <?php echo view('honeypot::honeypotFormFields'); ?>
                           <div class="form-group col-md-2">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Manu_Company')); ?> </label>
                              <select class="select2 form-control w-100" id="Company" name="Company" required>
                                 <option value=""><?php echo e(trans('admin.Manu_Company')); ?></option>
                                 <?php $__currentLoopData = $Companiesss; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $com): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                 <option value="<?php echo e($com->id); ?>" <?php if($Maint->Company == $com->id): ?> selected <?php endif; ?>>
     
                               <?php echo e(app()->getLocale() == 'ar' ?$com->Arabic_Name :$com->English_Name); ?>          
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>      
                              </select>
                           </div>
                           <div class="form-group col-md-2">
                              <label class="form-label" for="">  <?php echo e(trans('admin.Device_Type')); ?></label>
                              <select class="select2 form-control w-100" id="Device_Type" name="Device_Type" required>
                                 <?php if(!empty($Maint->Device_Type)): ?>
                                 <option value="<?php echo e($Maint->Device_Type); ?>">
                                  
                                         <?php echo e(app()->getLocale() == 'ar' ?$Maint->Device_Type()->first()->Arabic_Name :$Maint->Device_Type()->first()->English_Name); ?>    
                                  </option>
                                 <?php endif; ?>      
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for=""> <?php echo e(trans('admin.Device_Case')); ?> </label>
                              <select class="select2 form-control w-100" name="Device_Case">
                                 <option value=""> <?php echo e(trans('admin.Device_Case')); ?></option>
                                 <?php $__currentLoopData = $Cases; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $case): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($case->id); ?>" <?php if($Maint->Device_Case == $case->id): ?> selected <?php endif; ?>>
                               <?php echo e(app()->getLocale() == 'ar' ?$case->Arabic_Name :$case->English_Name); ?>                  
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for=""><?php echo e(trans('admin.Store')); ?></label>
                              <select class="select2 form-control w-100" id="store" name="Store" required>
                                 <option value=""> <?php echo e(trans('admin.Store')); ?></option>
                                 <?php $__currentLoopData = $Storess; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($stor->id); ?>" <?php if($Maint->Store == $stor->id): ?> selected <?php endif; ?>>
                                 <?php echo e(app()->getLocale() == 'ar' ?$stor->Name :$stor->NameEn); ?>     
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for=""><?php echo e(trans('admin.Eng')); ?></label>
                              <select class="select2 form-control w-100" name="Eng" required>
                                 <option value=""> <?php echo e(trans('admin.Eng')); ?></option>
                                 <?php $__currentLoopData = $Engs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $eng): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($eng->id); ?>" <?php if($Maint->Eng == $eng->id): ?> selected <?php endif; ?>>
                                        <?php echo e(app()->getLocale() == 'ar' ?$eng->Name :$eng->NameEn); ?>         
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for=""><?php echo e(trans('admin.Recipient')); ?></label>
                              <select class="select2 form-control w-100" name="Recipient" required>
                                 <option value=""> <?php echo e(trans('admin.Recipient')); ?></option>
                                 <?php $__currentLoopData = $Employess; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $emp): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($emp->id); ?>" <?php if($Maint->Recipient == $emp->id): ?> selected <?php endif; ?>>
                              <?php echo e(app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn); ?>           
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                              <select class="select2 form-control w-100" name="Coin" required>
                                 <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                 <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($coin->id); ?>" <?php if($Maint->Coin == $coin->id): ?> selected <?php endif; ?>>
                             <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?> 
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Draw')); ?></label>
                              <input type="number" step="any" name="Draw" value="<?php echo e($Maint->Draw); ?>" class="form-control" required>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for=""> <?php echo e(trans('admin.Cost_Center')); ?> </label>
                              <select class="select2 form-control w-100" name="Cost_Center" required>
                                 <option value=""> <?php echo e(trans('admin.Cost_Center')); ?></option>
                                 <?php $__currentLoopData = $CostCenters; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cost): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($cost->id); ?>" <?php if($Maint->Cost_Center == $cost->id): ?> selected <?php endif; ?>>
                                  <?php echo e(app()->getLocale() == 'ar' ?$cost->Arabic_Name :$cost->English_Name); ?> 
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for=""><?php echo e(trans('admin.Account')); ?></label>
                              <select  class="select2 form-control w-100" id="client" name="Client" required>
                                 <?php if(!empty($Maint->Client)): ?>
                                 <option value="<?php echo e($Maint->Client); ?>">
                                  
                                        <?php echo e(app()->getLocale() == 'ar' ?$Maint->Client()->first()->Name :$Maint->Client()->first()->NameEn); ?> 
                                  </option>
                                 <?php endif; ?>                       
                              </select>
                           </div>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for=""> <?php echo e(trans('admin.Sure_Bill')); ?> </label>
                              <select class="select2 form-control w-100" name="Sure">
                                 <option value=""> <?php echo e(trans('admin.Sure_Bill')); ?></option>
                                 <option value="1" <?php if($Maint->Sure == 1): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Auto')); ?></option> 
                                 <option value="0" <?php if($Maint->Sure == 0): ?> selected <?php endif; ?>> <?php echo e(trans('admin.By_Admin')); ?></option> 
                              </select>
                           </div>
                           <div class="col-md-12">
                              <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Save')); ?></button>
                           </div>
                        </form>
                        <?php else: ?>
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="<?php echo e(url('AddDefaultMaintainceFirst')); ?>">
                                       <?php echo e(trans('admin.AddNew')); ?>

                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <?php endif; ?>        
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-11" role="tabpanel">
                        <?php if(!empty($Manu)): ?>  
                        <form action="<?php echo e(url('AddDefaultManufacture')); ?>" method="post"  class="form-row">
                           <?php echo csrf_field(); ?>  
                           <?php echo view('honeypot::honeypotFormFields'); ?>
                            

                            
                    <div class="form-group col-md-2">
                                            <label class="form-label" for="">   <?php echo e(trans('admin.ManufacturingHalls')); ?> </label>
                                <select class="select2 form-control w-100" name="Hall" required>                
                                <option value=""> <?php echo e(trans('admin.ManufacturingHalls')); ?></option>
                                            <?php $__currentLoopData = $ManufacturingHalls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hall): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                    <option value="<?php echo e($hall->id); ?>" <?php if($Manu->Hall == $hall->id): ?> selected <?php endif; ?>>
                           
                               <?php echo e(app()->getLocale() == 'ar' ?$hall->Arabic_Name :$hall->English_Name); ?>             
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                            </select>
                                        </div> 
                
                           <div class="form-group col-lg-2">
                              <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                              <select class="select2 form-control w-100" name="Coin" required>
                                 <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                 <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($coin->id); ?>" <?php if($Manu->Coin == $coin->id): ?> selected <?php endif; ?>>
                                 <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?> 
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                              </select>
                           </div>
                            
                                    <div class="form-group col-lg-2">
                              <label class="form-label" for=""><?php echo e(trans('admin.Executing_Qty')); ?></label>
                              <select class="select2 form-control w-100" name="Executing_Qty" required>
                                 <option value=""> <?php echo e(trans('admin.Executing_Qty')); ?></option>
            
                                 <option value="1" <?php if($Manu->Executing_Qty == 1): ?> selected <?php endif; ?>>
                                 <?php echo e(trans('admin.Open')); ?>

                                 </option>
                                  
                                 <option value="0" <?php if($Manu->Executing_Qty == 0): ?> selected <?php endif; ?>>
                                 <?php echo e(trans('admin.Close')); ?>

                                 </option>
                     
                              </select>
                           </div>
                            
                            
                           <div class="form-group col-lg-2">
                              <label class="form-label" for="simpleinput"><?php echo e(trans('admin.Draw')); ?></label>
                              <input type="text" name="Draw" value="<?php echo e($Manu->Draw); ?>" class="form-control" required>
                           </div>
                            <?php if(auth()->guard('admin')->user()->email == '<EMAIL>'): ?>
                           <div class="form-group col-lg-2">
                              <label class="form-label" for=""> <?php echo e(trans('admin.Manufacturing_Type')); ?> </label>
                              <select class="select2 form-control w-100" name="Manu_Type">
                                 <option value=""> <?php echo e(trans('admin.Manufacturing_Type')); ?></option>
                    <option value="1" <?php if($Manu->Manu_Type == 1): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Big')); ?></option> 
                      <option value="0" <?php if($Manu->Manu_Type == 0): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Small')); ?></option> 
                              </select>
                           </div>
                            <?php endif; ?>
                           <div class="col-md-12">
                              <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Save')); ?></button>
                           </div>
                        </form>
                        <?php else: ?>
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="<?php echo e(url('AddDefaultManufactureFirst')); ?>">
                                       <?php echo e(trans('admin.AddNew')); ?>

                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <?php endif; ?>        
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-9" role="tabpanel">
                        <?php if(!empty($ShowHide)): ?>  
                        <form action="<?php echo e(url('AddDefaultShowHide')); ?>" method="post" enctype="multipart/form-data" >
                           <?php echo csrf_field(); ?>

                           <?php echo view('honeypot::honeypotFormFields'); ?>
                           <div class="form-row table-color2" style="border:1px solid #ccc;border-radius: 8px;padding:10px 5px;color:white;">
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Status')); ?></label>
                              <select class="select2 form-control w-100" name="Status"  required>
                              <option value="1" <?php if($ShowHide->Status == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Status == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Shipping_Company')); ?></label>
                              <select class="select2 form-control w-100" name="Shipping_Company"  required>
                              <option value="1" <?php if($ShowHide->Shipping_Company == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Shipping_Company == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Vendor_Date')); ?></label>
                              <select class="select2 form-control w-100" name="Vendor_Date"  required>
                              <option value="1" <?php if($ShowHide->Vendor_Date == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Vendor_Date == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Expire_Date')); ?></label>
                              <select class="select2 form-control w-100" name="Expire_Date"  required>
                              <option value="1" <?php if($ShowHide->Expire_Date == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Expire_Date == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3" style="display: none">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_BF_Taxes')); ?></label>
                              <select class="select2 form-control w-100" name="Total_BF_Taxes"   required>
                              <option value="1" <?php if($ShowHide->Total_BF_Taxes == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Total_BF_Taxes == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3" style="display: none">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_Taxes')); ?></label>
                              <select class="select2 form-control w-100" name="Total_Taxes"   required>
                              <option value="1" <?php if($ShowHide->Total_Taxes == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Total_Taxes == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                              <select class="select2 form-control w-100" name="Coin"  required>
                              <option value="1" <?php if($ShowHide->Coin == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Coin == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Cost_Center')); ?></label>
                              <select class="select2 form-control w-100" name="Cost_Center"  required>
                              <option value="1" <?php if($ShowHide->Cost_Center == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Cost_Center == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Draw')); ?></label>
                              <select class="select2 form-control w-100" name="Draw"   required>
                              <option value="1" <?php if($ShowHide->Draw == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Draw == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Delegate_Sale')); ?></label>
                              <select class="select2 form-control w-100" name="Delegate_Sale" required>
                              <option value="1" <?php if($ShowHide->Delegate_Sale == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Delegate_Sale == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Delegate_Purchase')); ?></label>
                              <select class="select2 form-control w-100" name="Delegate_Purchase" required>
                              <option value="1" <?php if($ShowHide->Delegate_Purchase == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Delegate_Purchase == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Note')); ?></label>
                              <select class="select2 form-control w-100" name="Note"    required>
                              <option value="1" <?php if($ShowHide->Note == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Note == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Refrence_Number')); ?></label>
                              <select class="select2 form-control w-100" name="Refrence_Number"    required>
                              <option value="1" <?php if($ShowHide->Refrence_Number == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Refrence_Number == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Branch')); ?></label>
                              <select class="select2 form-control w-100" name="Branch"    required>
                              <option value="1" <?php if($ShowHide->Branch == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Branch == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Serial_Num')); ?></label>
                              <select class="select2 form-control w-100" name="Serial_Num"    required>
                              <option value="1" <?php if($ShowHide->Serial_Num == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Serial_Num == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Pass')); ?></label>
                              <select class="select2 form-control w-100" name="Pass"    required>
                              <option value="1" <?php if($ShowHide->Pass == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Pass == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Pattern_Image')); ?></label>
                              <select class="select2 form-control w-100" name="Pattern_Image"    required>
                              <option value="1" <?php if($ShowHide->Pattern_Image == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Pattern_Image == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Search_Typical')); ?></label>
                              <select class="select2 form-control w-100" name="Search_Typical"    required>
                              <option value="1" <?php if($ShowHide->Search_Typical == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Search_Typical == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Validity_Product')); ?></label>
                              <select class="select2 form-control w-100" name="Validity_Product"    required>
                              <option value="1" <?php if($ShowHide->Validity_Product == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Yes')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Validity_Product == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.No')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.A4_Sales_Print')); ?></label>
                              <select class="select2 form-control w-100" name="A4"    required>
                              <option value="1" <?php if($ShowHide->A4 == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->A4 == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.A5_Sales_Print')); ?></label>
                              <select class="select2 form-control w-100" name="A5"    required>
                              <option value="1" <?php if($ShowHide->A5 == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->A5 == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.8CM_Sales_Print')); ?></label>
                              <select class="select2 form-control w-100" name="CM8"    required>
                              <option value="1" <?php if($ShowHide->CM8 == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->CM8 == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Manufacturing_Model_Shortcomings')); ?></label>
                              <select class="select2 form-control w-100" name="Manufacturing_Model_Shortcomings"    required>
                              <option value="1" <?php if($ShowHide->Manufacturing_Model_Shortcomings == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Manufacturing_Model_Shortcomings == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Group_Brand')); ?></label>
                              <select class="select2 form-control w-100" name="Group_Brand"    required>
                              <option value="1" <?php if($ShowHide->Group_Brand == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Group_Brand == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Patch_Number')); ?></label>
                              <select class="select2 form-control w-100" name="Patch_Number"    required>
                              <option value="1" <?php if($ShowHide->Patch_Number == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if($ShowHide->Patch_Number == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                                    <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Executor_Sale')); ?></label>
                              <select class="select2 form-control w-100" name="Executor_Sale"    required>
                              <option value="1" <?php if($ShowHide->Executor_Sale == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if($ShowHide->Executor_Sale == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>      
                               
                                    <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Totuch_Screen')); ?></label>
                              <select class="select2 form-control w-100" name="Totuch_Screen"  required>
                              <option value="1" <?php if($ShowHide->Totuch_Screen == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Totuch_Screen == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>       
                               
                                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Tax_POS')); ?></label>
                              <select class="select2 form-control w-100" name="Tax_POS"  required>
                              <option value="1" <?php if($ShowHide->Tax_POS == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Tax_POS == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div> 
                               
                                                   <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Icon_Payment_Recipt')); ?></label>
                              <select class="select2 form-control w-100" name="Icon_Payment_Recipt"  required>
                              <option value="1" <?php if($ShowHide->Icon_Payment_Recipt == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Icon_Payment_Recipt == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div> 
                               
                               
                                                   <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.SearchCode')); ?></label>
                              <select class="select2 form-control w-100" name="SearchCode"  required>
                              <option value="1" <?php if($ShowHide->SearchCode == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->SearchCode == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div> 
                               
                                                         <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.TaxOnTotal')); ?></label>
                              <select class="select2 form-control w-100" name="TaxOnTotal"  required>
                              <option value="1" <?php if($ShowHide->TaxOnTotal == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->TaxOnTotal == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div> 
                               
                                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_BF_Tax')); ?></label>
                              <select class="select2 form-control w-100" name="TotalBfTax"  required>
                              <option value="1" <?php if($ShowHide->TotalBfTax == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->TotalBfTax == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div> 
                                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.AvQty')); ?></label>
                              <select class="select2 form-control w-100" name="AvQty"  required>
                              <option value="1" <?php if($ShowHide->AvQty == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->AvQty == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div> 
                               
                                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Discount')); ?></label>
                              <select class="select2 form-control w-100" name="Disc"  required>
                              <option value="1" <?php if($ShowHide->Disc == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Disc == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div> 
                               
                                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Tax')); ?></label>
                              <select class="select2 form-control w-100" name="Tax"  required>
                              <option value="1" <?php if($ShowHide->Tax == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Tax == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div> 
                               
                                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Store')); ?></label>
                              <select class="select2 form-control w-100" name="Store"  required>
                              <option value="1" <?php if($ShowHide->Store == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Store == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div> 
                               
                               
                                                                                     <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.TaxBill')); ?></label>
                              <select class="select2 form-control w-100" name="TaxBill"  required>
                              <option value="1" <?php if($ShowHide->TaxBill == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->TaxBill == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div> 
     
                               
                                                                                                 <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Change_Way_Stores_Transfer')); ?></label>
                              <select class="select2 form-control w-100" name="Change_Way_Stores_Transfer"  required>
                              <option value="1" <?php if($ShowHide->Change_Way_Stores_Transfer == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Change_Way_Stores_Transfer == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div> 
                                                                                                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Note_POS')); ?></label>
                              <select class="select2 form-control w-100" name="Note_POS"  required>
                              <option value="1" <?php if($ShowHide->Note_POS == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Note_POS == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div> 
                                                                                                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Open_Drawer')); ?></label>
                              <select class="select2 form-control w-100" name="Open_Drawer"  required>
                              <option value="1" <?php if($ShowHide->Open_Drawer == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Open_Drawer == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div> 

                                                                                                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.client_delivery')); ?></label>
                              <select class="select2 form-control w-100" name="client_delivery"  required>
                              <option value="1" <?php if($ShowHide->client_delivery == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->client_delivery == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div> 
                              
                                                             <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.POS_RecivedDate')); ?></label>
                              <select class="select2 form-control w-100" name="POS_RecivedDate"    required>
                              <option value="1" <?php if($ShowHide->POS_RecivedDate == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->POS_RecivedDate == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                         
                               <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.POS_Qty')); ?></label>
                              <select class="select2 form-control w-100" name="POS_Qty"    required>
                              <option value="1" <?php if($ShowHide->POS_Qty == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->POS_Qty == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                    
                               <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.POS_Barcode')); ?></label>
                              <select class="select2 form-control w-100" name="POS_Barcode"    required>
                              <option value="1" <?php if($ShowHide->POS_Barcode == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->POS_Barcode == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                         
                                          <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Show_File_ReciptVoucher')); ?></label>
                              <select class="select2 form-control w-100" name="Show_File_ReciptVoucher"    required>
                              <option value="1" <?php if($ShowHide->Show_File_ReciptVoucher == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Show_File_ReciptVoucher == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                                   <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Show_File_PaymentVoucher')); ?></label>
                              <select class="select2 form-control w-100" name="Show_File_PaymentVoucher"    required>
                              <option value="1" <?php if($ShowHide->Show_File_PaymentVoucher == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Show_File_PaymentVoucher == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                   <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Show_File_Sales')); ?></label>
                              <select class="select2 form-control w-100" name="Show_File_Sales"    required>
                              <option value="1" <?php if($ShowHide->Show_File_Sales == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Show_File_Sales == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>   
                        
                                             <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Show_File_Purchases')); ?></label>
                              <select class="select2 form-control w-100" name="Show_File_Purchases"    required>
                              <option value="1" <?php if($ShowHide->Show_File_Purchases == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Show_File_Purchases == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                                   <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Show_File_Checks')); ?></label>
                              <select class="select2 form-control w-100" name="Show_File_Checks"    required>
                              <option value="1" <?php if($ShowHide->Show_File_Checks == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Show_File_Checks == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                   <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Show_File_InsurancePaper')); ?></label>
                              <select class="select2 form-control w-100" name="Show_File_InsurancePaper"    required>
                              <option value="1" <?php if($ShowHide->Show_File_InsurancePaper == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Show_File_InsurancePaper == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>   
                               
                                     <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Show_File_TransferStores')); ?></label>
                              <select class="select2 form-control w-100" name="Show_File_TransferStores"    required>
                              <option value="1" <?php if($ShowHide->Show_File_TransferStores == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Show_File_TransferStores == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                 
                               <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Thickness')); ?></label>
                              <select class="select2 form-control w-100" name="Thickness"    required>
                              <option value="1" <?php if($ShowHide->Thickness == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Thickness == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                 
                               
                               <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Height')); ?></label>
                              <select class="select2 form-control w-100" name="Height"    required>
                              <option value="1" <?php if($ShowHide->Height == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Height == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                
                               <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Items_Guide_Store_Show')); ?></label>
                              <select class="select2 form-control w-100" name="Items_Guide_Store_Show"    required>
                              <option value="1" <?php if($ShowHide->Items_Guide_Store_Show == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Items_Guide_Store_Show == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>       
                               <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Sales_Pro_Desc')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Desc"    required>
                              <option value="1" <?php if($ShowHide->Sales_Pro_Desc == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Sales_Pro_Desc == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         

                               
                           </div>
                           <br>
                           <h3>  <?php echo e(trans('admin.Print')); ?>  </h3>
                          
                           <div class="form-row table-color1" style="border:1px solid #ccc;border-radius: 8px;padding:10px 5px;">
                                   
                           <!--</div>-->
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Barcode_Print')); ?></label>
                              <select class="select2 form-control w-100" name="Barcode_Print"    required>
                              <option value="1" <?php if($ShowHide->Barcode_Print == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Barcode_Print == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Unit_Print')); ?></label>
                              <select class="select2 form-control w-100" name="Unit_Print"    required>
                              <option value="1" <?php if($ShowHide->Unit_Print == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Unit_Print == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_BF_Print')); ?></label>
                              <select class="select2 form-control w-100" name="Total_BF_Print"    required>
                              <option value="1" <?php if($ShowHide->Total_BF_Print == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Total_BF_Print == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Discount_Print')); ?></label>
                              <select class="select2 form-control w-100" name="Discount_Print"    required>
                              <option value="1" <?php if($ShowHide->Discount_Print == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Discount_Print == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                           <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Tax_Print')); ?></label>
                              <select class="select2 form-control w-100" name="Tax_Print"    required>
                              <option value="1" <?php if($ShowHide->Tax_Print == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Tax_Print == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.TotalDiscountPrint')); ?></label>
                              <select class="select2 form-control w-100" name="TotalDiscountPrint"    required>
                              <option value="1" <?php if($ShowHide->TotalDiscountPrint == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->TotalDiscountPrint == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.TotalTaxPrint')); ?></label>
                              <select class="select2 form-control w-100" name="TotalTaxPrint"    required>
                              <option value="1" <?php if($ShowHide->TotalTaxPrint == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->TotalTaxPrint == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.ProductsNumber')); ?></label>
                              <select class="select2 form-control w-100" name="ProductsNumber"    required>
                              <option value="1" <?php if($ShowHide->ProductsNumber == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->ProductsNumber == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.TotalQtyPrint')); ?></label>
                              <select class="select2 form-control w-100" name="TotalQtyPrint"    required>
                              <option value="1" <?php if($ShowHide->TotalQtyPrint == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->TotalQtyPrint == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Credit')); ?></label>
                              <select class="select2 form-control w-100" name="Credit"    required>
                              <option value="1" <?php if($ShowHide->Credit == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Credit == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Barcode')); ?></label>
                              <select class="select2 form-control w-100" name="Barcode"    required>
                              <option value="1" <?php if($ShowHide->Barcode == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Barcode == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Taknet')); ?></label>
                              <select class="select2 form-control w-100" name="Taknet"    required>
                              <option value="1" <?php if($ShowHide->Taknet == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Taknet == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Address')); ?></label>
                              <select class="select2 form-control w-100" name="Address"    required>
                              <option value="1" <?php if($ShowHide->Address == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Address == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Phone1')); ?></label>
                              <select class="select2 form-control w-100" name="Phone1"    required>
                              <option value="1" <?php if($ShowHide->Phone1 == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Phone1 == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Phone2')); ?></label>
                              <select class="select2 form-control w-100" name="Phone2"    required>
                              <option value="1" <?php if($ShowHide->Phone2 == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Phone2 == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Phone3')); ?></label>
                              <select class="select2 form-control w-100" name="Phone3"    required>
                              <option value="1" <?php if($ShowHide->Phone3 == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Phone3 == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Phone4')); ?></label>
                              <select class="select2 form-control w-100" name="Phone4"    required>
                              <option value="1" <?php if($ShowHide->Phone4 == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Phone4 == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Text')); ?></label>
                              <select class="select2 form-control w-100" name="Text"    required>
                              <option value="1" <?php if($ShowHide->Text == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Text == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                      <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Seal')); ?></label>
                              <select class="select2 form-control w-100" name="Seal"    required>
                              <option value="1" <?php if($ShowHide->Seal == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Seal == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
     
                                              <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Code_Report')); ?></label>
                              <select class="select2 form-control w-100" name="Code_Report"    required>
                              <option value="1" <?php if($ShowHide->Code_Report == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Code_Report == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                                                   <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Unit')); ?></label>
                              <select class="select2 form-control w-100" name="Unit"    required>
                              <option value="1" <?php if($ShowHide->Unit == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Unit == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                               
                                                   <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Refrence_Number')); ?></label>
                              <select class="select2 form-control w-100" name="Refrence_Number_Print"    required>
                              <option value="1" <?php if($ShowHide->Refrence_Number_Print == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Refrence_Number_Print == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                
                                                   <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Thickness_Print')); ?></label>
                              <select class="select2 form-control w-100" name="Thickness_Print"    required>
                              <option value="1" <?php if($ShowHide->Thickness_Print == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Thickness_Print == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                
                                                   <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Height_Print')); ?></label>
                              <select class="select2 form-control w-100" name="Height_Print"    required>
                              <option value="1" <?php if($ShowHide->Height_Print == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $ShowHide->Height_Print == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
                               
                            
                               
                           </div>
                           <div class="col-md-12 mt-3">
                              <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Save')); ?></button>
                           </div>
                        </form>
                        <?php else: ?>
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12 ">
                                       <a class="btn btn-primary" href="<?php echo e(url('AddDefaultShowHideFirst')); ?>">
                                       <?php echo e(trans('admin.AddNew')); ?>

                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <?php endif; ?>                   
                     </div>
                     <div class="tab-pane fade " id="tab_borders_icons-6" role="tabpanel">
                               
                            <?php if(!empty($Shipping)): ?>  
          <form action="<?php echo e(url('AddDefaultShipping')); ?>" method="post" enctype="multipart/form-data" class="form-row">
                                  <?php echo csrf_field(); ?>

               <?php echo view('honeypot::honeypotFormFields'); ?>
                       
                             <div class="form-group col-lg-4">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Delegate')); ?></label>
                                            <select class="select2 form-control w-100" name="Delegate" required>
                                          <option value=""> <?php echo e(trans('admin.Delegate')); ?></option>
                                            <?php $__currentLoopData = $Employess; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $emp): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                       <option value="<?php echo e($emp->id); ?>" <?php if($Shipping->Delegate == $emp->id): ?> selected <?php endif; ?>>
                               
                          <?php echo e(app()->getLocale() == 'ar' ?$emp->Name :$emp->NameEn); ?>  
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                            </select>
                                        </div>
                                            
                                            <div class="form-group col-lg-4">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Vendor')); ?></label>
                                            <select class="select2 form-control w-100" name="Vendor" required>
                                          <option value=""> <?php echo e(trans('admin.Vendor')); ?></option>
                                            <?php $__currentLoopData = $vendors; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vend): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($vend->id); ?>" <?php if($Shipping->Vendor == $vend->id): ?> selected <?php endif; ?>>
                       
                                                    
                                      <?php echo e(app()->getLocale() == 'ar' ?$vend->Name :$vend->NameEn); ?>                  
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                            </select>
                                        </div>
                                            
                                            <div class="form-group col-lg-4">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Client')); ?></label>
    <select class="select2 form-control w-100"  name="Client"  required>
                                          <option value=""> <?php echo e(trans('admin.Client')); ?></option>
                                            <?php $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $cli): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($cli->id); ?>" <?php if($Shipping->Client == $cli->id): ?> selected <?php endif; ?>>
                                             <?php echo e(app()->getLocale() == 'ar' ?$cli->Name :$cli->NameEn); ?>                   
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                            </select>
                                        </div>

                               <div class="form-group col-lg-4">
                                            <label class="form-label" for=""><?php echo e(trans('admin.ShippingType')); ?></label>
                                            <select class="select2 form-control w-100" name="Type" required>
                                          <option value=""> <?php echo e(trans('admin.ShippingType')); ?></option>
                                            <?php $__currentLoopData = $Types; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $typ): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($typ->id); ?>" <?php if($Shipping->Type == $typ->id): ?> selected <?php endif; ?>>
                                         
                                               <?php echo e(app()->getLocale() == 'ar' ?$typ->Arabic_Name :$typ->English_Name); ?>         
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                            </select>
                                        </div>    
    
             <div class="form-group col-lg-4">
                                            <label class="form-label" for=""><?php echo e(trans('admin.ShippingStatus')); ?></label>
                                            <select class="select2 form-control w-100" name="Status" required>
                                          <option value=""> <?php echo e(trans('admin.ShippingStatus')); ?></option>
                                            <?php $__currentLoopData = $Status; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $st): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($st->id); ?>" <?php if($Shipping->Status == $st->id): ?> selected <?php endif; ?>> 
                                               <?php echo e(app()->getLocale() == 'ar' ?$st->Arabic_Name :$st->English_Name); ?>                
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                            </select>
                                        </div>    
                             <div class="form-group col-lg-4">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Breakable')); ?></label>
                                            <select class="select2 form-control w-100" name="Breakable" required>
                                          <option value=""> <?php echo e(trans('admin.Breakable')); ?></option>
                                          <option value="1" <?php if($Shipping->Breakable == 1): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Yes')); ?></option>
                                          <option value="0" <?php if($Shipping->Breakable == 0): ?> selected <?php endif; ?>> <?php echo e(trans('admin.No')); ?></option>
                                            </select>
                                        </div>       
                                               <div class="form-group col-lg-4">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                                            <select class="select2 form-control w-100" name="Coin" required>
                                                 <option value=""> <?php echo e(trans('admin.Coin')); ?></option>
                                            <?php $__currentLoopData = $Coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                                <option value="<?php echo e($coin->id); ?>" <?php if($Shipping->Coin == $coin->id): ?> selected <?php endif; ?>>
                                               <?php echo e(app()->getLocale() == 'ar' ?$coin->Arabic_Name :$coin->English_Name); ?> 
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>  
                                            </select>
                                        </div>
              
                        <div class="form-group col-lg-4">
                              <label class="form-label" for=""><?php echo e(trans('admin.Safe')); ?></label>
                              <select class="select2 form-control w-100" name="Safe" required>
                                 <option value=""> <?php echo e(trans('admin.Safe')); ?></option>
                                 <?php $__currentLoopData = $Safes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $safe): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>    
                                 <option value="<?php echo e($safe->id); ?>" <?php if($safe->id  == $Shipping->Safe): ?> selected <?php endif; ?>>
                             <?php echo e(app()->getLocale() == 'ar' ?$safe->Name :$safe->NameEn); ?>         
                                 </option>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>    
                              </select>
                           </div>
              
                <div class="form-group col-lg-4">
                                            <label class="form-label" for=""><?php echo e(trans('admin.Payment_Method')); ?></label>
                                            <select class="select2 form-control w-100" name="Payment_Method" required>
                                          <option value=""> <?php echo e(trans('admin.Payment_Method')); ?></option>
                                          <option value="Cash" <?php if($Shipping->Payment_Method == 'Cash'): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Cash')); ?></option>
                                          <option value="Later" <?php if($Shipping->Payment_Method == 'Later'): ?> selected <?php endif; ?>> <?php echo e(trans('admin.Later')); ?></option>
                                            </select>
                                        </div>    
              
              
              
              
                 <div class="form-group col-lg-12">
                     <h2> <?php echo e(trans('admin.Show_Hide_Data')); ?></h2>
                    </div>
              
              
                       <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                              <select class="select2 form-control w-100" name="Show_Coin"    required>
                              <option value="1" <?php if($Shipping->Show_Coin == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $Shipping->Show_Coin == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
              
                         <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Draw')); ?></label>
                              <select class="select2 form-control w-100" name="Show_Draw"    required>
                              <option value="1" <?php if($Shipping->Show_Draw == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $Shipping->Show_Draw == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
              
                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Safe')); ?></label>
                              <select class="select2 form-control w-100" name="Show_Safe"    required>
                              <option value="1" <?php if($Shipping->Show_Safe == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $Shipping->Show_Safe == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
              
                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Code')); ?></label>
                              <select class="select2 form-control w-100" name="Show_Code"    required>
                              <option value="1" <?php if($Shipping->Show_Code == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $Shipping->Show_Code == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
              
              
                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Weight')); ?></label>
                              <select class="select2 form-control w-100" name="Show_Weight"    required>
                              <option value="1" <?php if($Shipping->Show_Weight == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $Shipping->Show_Weight == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
              
              
                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Width')); ?></label>
                              <select class="select2 form-control w-100" name="Show_Width"    required>
                              <option value="1" <?php if($Shipping->Show_Width == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $Shipping->Show_Width == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
              
              
                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Length')); ?></label>
                              <select class="select2 form-control w-100" name="Show_Length"    required>
                              <option value="1" <?php if($Shipping->Show_Length == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $Shipping->Show_Length == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
              
                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Height')); ?></label>
                              <select class="select2 form-control w-100" name="Show_Height"    required>
                              <option value="1" <?php if($Shipping->Show_Height == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $Shipping->Show_Height == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>
              
              








              
                                                                              <div class="col-md-12">
                                <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Save')); ?></button>
                                                    </div> 
                                                </form>         
                                    <?php else: ?>
         <div  class="form-row">
           <div class="col-md-12">
              <div class="data-def">
          <div class="form-row">
               <div class="form-group col-md-12">
              <a class="btn btn-primary" href="<?php echo e(url('AddDefaultShippingFirst')); ?>">
                    <?php echo e(trans('admin.AddNew')); ?>

                   </a>   
              </div>
              </div>
               </div>
             </div>
                      </div>
                                    <?php endif; ?>                   
                                                
                                                
                                            </div>     
                     <div class="tab-pane fade" id="tab_borders_icons-7" role="tabpanel">
                        <?php if(!empty($CustomPrint)): ?>          
                        <form action="<?php echo e(url('AddDefaultCustomPrint')); ?>" method="post"  class="form-row">
                           <?php echo csrf_field(); ?>  
                           <?php echo view('honeypot::honeypotFormFields'); ?>
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                             
                                              <div class="form-group col-lg-12">
                                                  <h1 class="text-center head"><?php echo e(trans('admin.Sales')); ?></h1>              
                                                </div>
                                     
                                     
                                     
                                     
                                              <div class="form-group col-lg-12">
                              <label class="form-label" for=""><?php echo e(trans('admin.Print_Type')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Print_Type"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Print_Type == 1): ?> selected <?php endif; ?>>Landscape </option>
                              <option value="2" <?php if( $CustomPrint->Sales_Print_Type == 2): ?> selected <?php endif; ?>>Portarit</option>
                              </select>
                           </div>
                                     
                                                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Bill_Code')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Bill_Code"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Bill_Code == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Bill_Code == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>      
                                     
                                     <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Date')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Date"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Date == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Date == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                     
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Coin"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Coin == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Coin == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Draw')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Draw"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Draw == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Draw == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Payment_Method')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Payment_Method"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Payment_Method == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Payment_Method == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Status')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Status"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Status == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Status == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Executor')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Executor"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Executor == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Executor == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Refernce_Number')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Refernce_Number"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Refernce_Number == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Refernce_Number == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Safe')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Safe"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Safe == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Safe == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Client')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Client"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Client == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Client == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Delegate')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Delegate"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Delegate == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Delegate == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Store')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Store"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Store == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Store == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-4">
                              <label class="form-label" for=""><?php echo e(trans('admin.User')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_User"  required>
                              <option value="1" <?php if($CustomPrint->Sales_User == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_User == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-4">
                              <label class="form-label" for=""><?php echo e(trans('admin.Cost_Center')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Cost_Center"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Cost_Center == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Cost_Center == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                   
                                                        <div class="form-group col-lg-4">
                              <label class="form-label" for=""><?php echo e(trans('admin.Notes')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Notes"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Notes == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Notes == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                     
                                     
                                       <div class="form-group col-lg-12">
                                                      <h4 class="text-center suppp"><?php echo e(trans('admin.Products')); ?></h4>    
                                     </div>
                               
                                     
                                     
                                                           
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Product_Code')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Code"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Pro_Code == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Pro_Code == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Product_Name')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Name"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Pro_Name == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Pro_Name == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Unit')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Unit"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Pro_Unit == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Pro_Unit == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Qty')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Qty"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Pro_Qty == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Pro_Qty == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                              
                                     
                                                                      
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Price')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Price"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Pro_Price == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Pro_Price == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Discount')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Discount"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Pro_Discount == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Pro_Discount == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_Bf_Tax')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Total_Bf_Tax"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Pro_Total_Bf_Tax == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Pro_Total_Bf_Tax == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_Tax')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Total_Tax"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Pro_Total_Tax == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Pro_Total_Tax == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                              
                                     
                                                                      
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Total"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Pro_Total == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Pro_Total == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Store')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Store"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Pro_Store == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Pro_Store == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Desc')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Desc"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Pro_Desc == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Pro_Desc == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Exp_Date')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Exp_Date"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Pro_Exp_Date == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Pro_Exp_Date == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                              
                                     
                                                                      
                                                        <div class="form-group col-lg-6">
                              <label class="form-label" for=""><?php echo e(trans('admin.Weight')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Weight"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Pro_Weight == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Pro_Weight == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-6">
                              <label class="form-label" for=""><?php echo e(trans('admin.Patch_Number')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Pro_Patch_Number"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Pro_Patch_Number == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Pro_Patch_Number == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                         
                                                    
                                       <div class="form-group col-lg-12">
                                                      <h4 class="text-center suppp"><?php echo e(trans('admin.Footer')); ?></h4>    
                                     </div>
                               
                                     
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Product_Numbers')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Product_Numbers"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Product_Numbers == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Product_Numbers == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_Qty')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Total_Qty"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Total_Qty == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Total_Qty == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_Discount')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Total_Discount"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Total_Discount == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Total_Discount == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_Bf_Taxes')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Total_Bf_Taxes"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Total_Bf_Taxes == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Total_Bf_Taxes == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_Taxes')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Total_Taxes"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Total_Taxes == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Total_Taxes == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_Price')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Total_Price"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Total_Price == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Total_Price == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Paid')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Paid"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Paid == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Paid == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Residual')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Residual"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Residual == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Residual == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Taknet')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Taknet"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Taknet == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Taknet == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Credit')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Credit"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Credit == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Credit == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Barcode')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Barcode"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Barcode == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Barcode == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Text')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Text"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Text == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Text == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-12">
                              <label class="form-label" for=""><?php echo e(trans('admin.Seal')); ?></label>
                              <select class="select2 form-control w-100" name="Sales_Seal"  required>
                              <option value="1" <?php if($CustomPrint->Sales_Seal == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Sales_Seal == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                       


                                     
                                      <div class="form-group col-lg-12">
                                                  <h1 class="text-center head"><?php echo e(trans('admin.Purchases')); ?></h1>              
                                                </div>
                                     
                                     
                                     
                                     
                                              <div class="form-group col-lg-12">
                              <label class="form-label" for=""><?php echo e(trans('admin.Print_Type')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Print_Type"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Print_Type == 1): ?> selected <?php endif; ?>>Landscape </option>
                              <option value="2" <?php if( $CustomPrint->Purch_Print_Type == 2): ?> selected <?php endif; ?>>Portarit</option>
                              </select>
                           </div>
                                     
                                                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Bill_Code')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Bill_Code"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Bill_Code == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Bill_Code == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>      
                                     
                                     <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Date')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Date"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Date == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Date == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                     
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Vendor_Bill_Date')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Vendor_Bill_Date"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Vendor_Bill_Date == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Vendor_Bill_Date == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Coin')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Coin"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Coin == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Coin == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Draw')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Draw"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Draw == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Draw == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Payment_Method')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Payment_Method"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Payment_Method == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Payment_Method == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Status')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Status"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Status == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Status == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Refernce_Number')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Refernce_Number"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Refernce_Number == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Refernce_Number == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Safe')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Safe"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Safe == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Safe == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Vendor')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Vendor"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Vendor == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Vendor == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Delegate')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Delegate"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Delegate == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Delegate == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Store')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Store"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Store == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Store == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                          
                                            <div class="form-group col-lg-4">
                              <label class="form-label" for=""><?php echo e(trans('admin.User')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_User"  required>
                              <option value="1" <?php if($CustomPrint->Purch_User == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_User == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                        <div class="form-group col-lg-4">
                              <label class="form-label" for=""><?php echo e(trans('admin.Cost_Center')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Cost_Center"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Cost_Center == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Cost_Center == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                                   
                                                        <div class="form-group col-lg-4">
                              <label class="form-label" for=""><?php echo e(trans('admin.Notes')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Notes"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Notes == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Notes == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                     
                                     
                                     
                                       <div class="form-group col-lg-12">
                                                      <h4 class="text-center suppp"><?php echo e(trans('admin.Products')); ?></h4>    
                                     </div>
                               
                                     
                                     
                                                           
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Product_Code')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Code"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Pro_Code == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Pro_Code == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Product_Name')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Name"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Pro_Name == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Pro_Name == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Unit')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Unit"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Pro_Unit == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Pro_Unit == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Qty')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Qty"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Pro_Qty == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Pro_Qty == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                              
                                     
                                                                      
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Price')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Price"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Pro_Price == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Pro_Price == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Discount')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Discount"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Pro_Discount == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Pro_Discount == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_Bf_Tax')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Total_Bf_Tax"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Pro_Total_Bf_Tax == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Pro_Total_Bf_Tax == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_Tax')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Total_Tax"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Pro_Total_Tax == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Pro_Total_Tax == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                              
                                     
                                                                      
                                                        <div class="form-group col-lg-4">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Total"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Pro_Total == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Pro_Total == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                                                        <div class="form-group col-lg-4">
                              <label class="form-label" for=""><?php echo e(trans('admin.Store')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Store"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Pro_Store == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Pro_Store == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                                                     
                                                   
                  
                                                                     
                                                   
                                                        <div class="form-group col-lg-4">
                              <label class="form-label" for=""><?php echo e(trans('admin.Exp_Date')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Pro_Exp_Date"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Pro_Exp_Date == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Pro_Exp_Date == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                              
                                     
                                                              
                         
                                                    
                                       <div class="form-group col-lg-12">
                                                      <h4 class="text-center suppp"><?php echo e(trans('admin.Footer')); ?></h4>    
                                     </div>
                               
                                     
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Product_Numbers')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Product_Numbers"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Product_Numbers == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Product_Numbers == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_Qty')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Total_Qty"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Total_Qty == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Total_Qty == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_Discount')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Total_Discount"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Total_Discount == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Total_Discount == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_Bf_Taxes')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Total_Bf_Taxes"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Total_Bf_Taxes == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Total_Bf_Taxes == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_Taxes')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Total_Taxes"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Total_Taxes == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Total_Taxes == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Total_Price')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Total_Price"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Total_Price == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Total_Price == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Paid')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Paid"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Paid == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Paid == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Residual')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Residual"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Residual == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Residual == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Taknet')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Taknet"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Taknet == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Taknet == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Credit')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Credit"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Credit == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Credit == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Barcode')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Barcode"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Barcode == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Barcode == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-3">
                              <label class="form-label" for=""><?php echo e(trans('admin.Text')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Text"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Text == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Text == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>                   
                                <div class="form-group col-lg-12">
                              <label class="form-label" for=""><?php echo e(trans('admin.Seal')); ?></label>
                              <select class="select2 form-control w-100" name="Purch_Seal"  required>
                              <option value="1" <?php if($CustomPrint->Purch_Seal == 1): ?> selected <?php endif; ?>><?php echo e(trans('admin.Show')); ?> </option>
                              <option value="0" <?php if( $CustomPrint->Purch_Seal == 0): ?> selected <?php endif; ?>><?php echo e(trans('admin.Hide')); ?></option>
                              </select>
                           </div>         
                                       
         
                                     
                                     
                                 </div>
                                 <div class="form-row mt-2">
                                    <button type="submit" class="btn btn-primary"><?php echo e(trans('admin.Save')); ?></button>
                                 </div>
                              </div>
                           </div>
                        </form>
                        <?php else: ?>
                        <div  class="form-row">
                           <div class="col-md-12">
                              <div class="data-def">
                                 <div class="form-row">
                                    <div class="form-group col-md-12">
                                       <a class="btn btn-primary" href="<?php echo e(url('AddDefaultCustomPrintFirst')); ?>">
                                       <?php echo e(trans('admin.AddNew')); ?>

                                       </a>   
                                    </div>
                                 </div>
                              </div>
                           </div>
                        </div>
                        <?php endif; ?>            
                     </div>
      
                      
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</main>

<style>
    .suppp{
            font-size: 20px;
    border: 1px solid darkorchid;
    padding: 7px;
    background: darkseagreen;
        
    }
    
    .head{
            font-size: 30px;
    border: 1px solid darkorange;
    padding: 7px;
    background: antiquewhite;
    }
</style>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/datagrid/datatables/datatables.bundle.css')); ?>">
<link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/summernote/summernote.css')); ?>">
<link rel="stylesheet" media="screen, print" href="<?php echo e(asset('Admin/css/formplugins/select2/select2.bundle.css')); ?>">
<script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.bundle.js')); ?>"></script>
<script src="<?php echo e(asset('Admin/js/datagrid/datatables/datatables.export.js')); ?>"></script>
<script src="<?php echo e(asset('Admin/js/formplugins/summernote/summernote.js')); ?>"></script>
<script src="<?php echo e(asset('Admin/js/formplugins/select2/select2.bundle.js')); ?>"></script>
<script>
   //_fnFeatureHtmlLength();
   $(document).ready(function () {
       // Setup - add a text input to each footer cell
       $('#dt-basic-example thead tr').clone(true).appendTo('#dt-basic-example thead');
       $('#dt-basic-example thead tr:eq(1) th').each(function (i) {
           var title = $(this).text();
           $(this).html('<input type="text" class="form-control form-control-sm" placeholder="Search ' + title + '" />');
   
           $('input', this).on('keyup change', function () {
               if (table.column(i).search() !== this.value) {
                   table
                       .column(i)
                       .search(this.value)
                       .draw();
               }
           });
       });
       var table = $('#dt-basic-example').DataTable(
           {
               responsive: true,
               orderCellsTop: true,
               fixedHeader: true,
               lengthChange: true,
   
               dom: "<'row mb-3'<'col-sm-12 col-md-3 d-flex align-items-center justify-content-start'f><'col-sm-12 col-md-9 d-flex align-items-center justify-content-end'B>>" +
                   "<'row'<'col-sm-12'tr>>" +
                   "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
   
               buttons: [
                   {
                       extend: 'pageLength',
                       className: 'btn-outline-default'
                   },
                   {
                       extend: 'colvis',
                       text: 'Column Visibility',
                       titleAttr: 'Col visibility',
                       className: 'btn-outline-default'
                   },
                   {
                       extend: 'pdfHtml5',
                       text: 'PDF',
                       titleAttr: 'Generate PDF',
                       className: 'btn-outline-danger btn-sm mr-1'
                   },
                   {
                       extend: 'excelHtml5',
                       text: 'Excel',
                       titleAttr: 'Generate Excel',
                       className: 'btn-outline-success btn-sm mr-1'
                   },
                   {
                       extend: 'csvHtml5',
                       text: 'CSV',
                       titleAttr: 'Generate CSV',
                       className: 'btn-outline-primary btn-sm mr-1'
                   },
                   {
                       extend: 'copyHtml5',
                       text: 'Copy',
                       titleAttr: 'Copy to clipboard',
                       className: 'btn-outline-primary btn-sm mr-1'
                   },
                   {
                       extend: 'print',
                       text: 'Print',
                       titleAttr: 'Print Table',
                       className: 'btn-outline-primary btn-sm'
                   }
               ],
           });
       $('.js-thead-colors a').on('click', function () {
           var theadColor = $(this).attr("data-bg");
           console.log(theadColor);
           $('#dt-basic-example thead').removeClassPrefix('bg-').addClass(theadColor);
       });
   
       $('.js-tbody-colors a').on('click', function () {
           var theadColor = $(this).attr("data-bg");
           console.log(theadColor);
           $('#dt-basic-example').removeClassPrefix('bg-').addClass(theadColor);
       });
   
   });
   
</script>
<!-- Search Selecet -->
<script>
   $(document).ready(function()
   {
       $(function()
       {
           $('.select2').select2();
   
           $(".select2-placeholder-multiple").select2(
           {
               placeholder: "Select State"
           });
           $(".js-hide-search").select2(
           {
               minimumResultsForSearch: 1 / 0
           });
           $(".js-max-length").select2(
           {
               maximumSelectionLength: 2,
               placeholder: "Select maximum 2 items"
           });
           $(".select2-placeholder").select2(
           {
               placeholder: "Select a state",
               allowClear: true
           });
   
           $(".js-select2-icons").select2(
           {
               minimumResultsForSearch: 1 / 0,
               templateResult: icon,
               templateSelection: icon,
               escapeMarkup: function(elm)
               {
                   return elm
               }
           });
   
           function icon(elm)
           {
               elm.element;
               return elm.id ? "<i class='" + $(elm.element).data("icon") + " mr-2'></i>" + elm.text : elm.text
           }
   
   
   $('#AccountDificit').select2({
   placeholder: "select...",
   ajax: {
   type: "GET",
   dataType: 'json',
   url: 'AllSubAccounts',
   processResults: function (data) {
     return {
       results: $.map(data, function(obj, index) {
         return { id: index, text: obj };
       })
     };
       
       	console.log(data);
       
   },
   data: function (params) {  
     var query = {
       search: params.term
     };
     if (params.term == "*") query.items = [];
     return { json: JSON.stringify( query ) }
   }
   }
   });
   
   
   $('#AccountDificit').on('select2:select', function (e) {
   console.log("select done", e.params.data);
   });
      
           
   $('#AccountExcess').select2({
   placeholder: "select...",
   ajax: {
   type: "GET",
   dataType: 'json',
   url: 'AllSubAccounts',
   processResults: function (data) {
     return {
       results: $.map(data, function(obj, index) {
         return { id: index, text: obj };
       })
     };
       
       	console.log(data);
       
   },
   data: function (params) {  
     var query = {
       search: params.term
     };
     if (params.term == "*") query.items = [];
     return { json: JSON.stringify( query ) }
   }
   }
   });
   
   
   $('#AccountExcess').on('select2:select', function (e) {
   console.log("select done", e.params.data);
   });
                      
   
           
         $("#client").select2({
           placeholder: "select...",
           ajax: {
               type: "GET",
               dataType: "json",
               url: "AllClientsFilter",
               processResults: function (data) {
                   return {
                       results: $.map(data, function (obj, index) {
   
                           return { id: index, text: obj };
                       }),
                   };
   
                   console.log(data);
               },
             data: function (params) {  
   
       
              var query = {
                       search: params.term,
                   };
                           
       
         $.ajax({
                         url: 'AllClientsFilterJS/'+params.term,
                         type:"GET",
                         dataType:"json",
                         beforeSend: function(){
                             $('#loader').css("visibility", "visible");
                         },
   
                         success:function(data) {
                                     $('#client').empty();  
                             $.each(data, function(key, value){
   
                    $('#client').append('<option value="'+ key +'">' + value + '</option>');
              
                             });
                         },
                         complete: function(){
                             $('#loader').css("visibility", "hidden");
                         }
                     });
       
       
       
   }
           },
       });
   
       $("#client").on("select2:select", function (e) {
           console.log("select done", e.params.data);
       });
       
                   
           
       });
   });
</script>
<!-- Filter Governrate and City !-->
<script>
   $(document).ready(function() {
   
       $('#Governrate').on('change', function(){
           var countryId = $(this).val();
           if(countryId) {
               $.ajax({
                   url: 'GovernrateFilter/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#City').empty();
   
                       $.each(data, function(key, value){
   
             $('#City').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {
   
               $('select[name="states"]').empty();
           }
   
       });
   
   });
</script>

<!-- Filter Governrate and City Company Data !-->
<script>
   $(document).ready(function() {
   
       $('.Governrate').on('change', function(){
           var countryId = $(this).val();
           if(countryId) {
               $.ajax({
                   url: 'GovernrateFilter/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('.City').empty();
   
                       $.each(data, function(key, value){
   
             $('.City').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
                      var CIITY = $('.City').val();  
                         $.ajax({
                   url: 'CityFilter/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('.Place').empty();
   
                       $.each(data, function(key, value){
   
             $('.Place').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                       
                               
                       
       
                       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {
   
               $('select[name="states"]').empty();
           }
   
       });
   
   });

    
 $('.City').on('change', function(){
      var CIITY = $('.City').val();  
                         $.ajax({
                   url: 'CityFilter/'+CIITY,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('.Place').empty();
   
                       $.each(data, function(key, value){
   
             $('.Place').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                       
            
                       
       
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
                       
     
 });
    
</script>

<!-- Filter Platform and Campaigns !-->
<script>
   $(document).ready(function() {
   
       $('#Platform').on('change', function(){
           var countryId = $(this).val();
           if(countryId) {
               $.ajax({
                   url: 'PlatformFilter/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#Campagin').empty();
   
                       $.each(data, function(key, value){
   
             $('#Campagin').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {
   
               $('select[name="states"]').empty();
           }
   
       });
   
   });
</script>
<script src="js/formplugins/summernote/summernote.js"></script>
<script>
   var autoSave = $('#autoSave');
   var interval;
   var timer = function()
   {
       interval = setInterval(function()
       {
           //start slide...
           if (autoSave.prop('checked'))
               saveToLocal();
   
           clearInterval(interval);
       }, 3000);
   };
   
   //save
   var saveToLocal = function()
   {
       localStorage.setItem('summernoteData', $('#saveToLocal').summernote("code"));
       console.log("saved");
   }
   
   //delete 
   var removeFromLocal = function()
   {
       localStorage.removeItem("summernoteData");
       $('#saveToLocal').summernote('reset');
   }
   
   $(document).ready(function()
   {
       //init default
       $('.js-summernote').summernote(
       {
           height: 200,
           tabsize: 2,
           placeholder: "Type here...",
           dialogsFade: true,
           toolbar: [
               ['style', ['style']],
               ['font', ['strikethrough', 'superscript', 'subscript']],
               ['font', ['bold', 'italic', 'underline', 'clear']],
               ['fontsize', ['fontsize']],
               ['fontname', ['fontname']],
               ['color', ['color']],
               ['para', ['ul', 'ol', 'paragraph']],
               ['height', ['height']]
               ['table', ['table']],
               ['insert', ['link', 'picture', 'video']],
               ['view', ['fullscreen', 'codeview', 'help']]
           ],
           callbacks:
           {
               //restore from localStorage
               onInit: function(e)
               {
                   $('.js-summernote').summernote("code", localStorage.getItem("summernoteData"));
               },
               onChange: function(contents, $editable)
               {
                   clearInterval(interval);
                   timer();
               }
           }
       });
   
       //load emojis
       $.ajax(
       {
           url: 'https://api.github.com/emojis',
           async: false
       }).then(function(data)
       {
           window.emojis = Object.keys(data);
           window.emojiUrls = data;
       });
   
       //init emoji example
       $(".js-hint2emoji").summernote(
       {
           height: 100,
           toolbar: false,
           placeholder: 'type starting with : and any alphabet',
           hint:
           {
               match: /:([\-+\w]+)$/,
               search: function(keyword, callback)
               {
                   callback($.grep(emojis, function(item)
                   {
                       return item.indexOf(keyword) === 0;
                   }));
               },
               template: function(item)
               {
                   var content = emojiUrls[item];
                   return '<img src="' + content + '" width="20" /> :' + item + ':';
               },
               content: function(item)
               {
                   var url = emojiUrls[item];
                   if (url)
                   {
                       return $('<img />').attr('src', url).css('width', 20)[0];
                   }
                   return '';
               }
           }
       });
   
       //init mentions example
       $(".js-hint2mention").summernote(
       {
           height: 100,
           toolbar: false,
           placeholder: "type starting with @",
           hint:
           {
               mentions: ['jayden', 'sam', 'alvin', 'david'],
               match: /\B@(\w*)$/,
               search: function(keyword, callback)
               {
                   callback($.grep(this.mentions, function(item)
                   {
                       return item.indexOf(keyword) == 0;
                   }));
               },
               content: function(item)
               {
                   return '@' + item;
               }
           }
       });
   
   });
   
</script>
<!-- Filter Manufactuer Company and Device Type !-->
<script>
   $(document).ready(function() {
   
       $('#Company').on('change', function(){
           var countryId = $(this).val();
           if(countryId) {
               $.ajax({
                   url: 'CompanyManufactureFilter/'+countryId,
                   type:"GET",
                   dataType:"json",
                   beforeSend: function(){
                       $('#loader').css("visibility", "visible");
                   },
   
                   success:function(data) {
   
                       $('#Device_Type').empty();
   
                       $.each(data, function(key, value){
   
             $('#Device_Type').append('<option value="'+ key +'">' + value + '</option>');
                           
                       });
                   },
                   complete: function(){
                       $('#loader').css("visibility", "hidden");
                   }
               });
           } else {
   
               $('select[name="states"]').empty();
           }
   
       });
   
   });
</script>


<!-- Hall Service -->
<script>
 function HallService(){
     var Hall_Service_Type=$('#Hall_Service_Type').val();
     
     if(parseFloat(Hall_Service_Type) == 1){
        
         document.getElementById('Hall_Service_Precent').style.display='block';
        }else{
        document.getElementById('Hall_Service_Precent').style.display='none';  
        }
     
     
 }
    
    
     $(document).ready(function() {
         
           var Hall_Service_Type=$('#Hall_Service_Type').val();
     
     if(parseFloat(Hall_Service_Type) == 1){
        
         document.getElementById('Hall_Service_Precent').style.display='block';
        }else{
        document.getElementById('Hall_Service_Precent').style.display='none';  
        }   
         
         
     });


</script>


<?php $__env->stopPush(); ?>
<?php echo $__env->make('admin.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost-erp-online\resources\views/admin/Settings/Default_Data.blade.php ENDPATH**/ ?>