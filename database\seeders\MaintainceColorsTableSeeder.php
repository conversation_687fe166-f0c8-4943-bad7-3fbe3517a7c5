<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class MaintainceColorsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('maintaince_colors')->delete();
        
        \DB::table('maintaince_colors')->insert(array (
            0 => 
            array (
                'id' => 3,
                'Refuse' => NULL,
                'Reported_Client' => NULL,
                'Refused_Client' => NULL,
                'Edited' => NULL,
                'created_at' => NULL,
                'updated_at' => NULL,
            ),
            1 => 
            array (
                'id' => 4,
                'Refuse' => '#000000',
                'Reported_Client' => '#000000',
                'Refused_Client' => '#000000',
                'Edited' => '#000000',
                'created_at' => '2022-04-13 02:50:56',
                'updated_at' => '2022-04-13 02:50:56',
            ),
        ));
        
        
    }
}