<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class CoinsTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('coins')->delete();
        
        \DB::table('coins')->insert(array (
            0 => 
            array (
                'id' => 5,
                'Arabic_Name' => 'درهم',
                'English_Name' => 'Darhm',
                'created_at' => '2021-06-16 01:47:18',
                'updated_at' => '2022-06-08 17:40:35',
                'Draw' => '5',
                'Symbol' => 'AED',
                'Code' => NULL,
            ),
            1 => 
            array (
                'id' => 3,
                'Arabic_Name' => 'دينار',
                'English_Name' => 'Dinar',
                'created_at' => '2021-06-16 01:46:47',
                'updated_at' => '2022-06-08 17:41:09',
                'Draw' => '60',
                'Symbol' => 'KWD',
                'Code' => NULL,
            ),
            2 => 
            array (
                'id' => 4,
                'Arabic_Name' => 'ريال سعودي',
                'English_Name' => 'SAR',
                'created_at' => '2021-06-16 01:47:03',
                'updated_at' => '2022-06-08 17:41:22',
                'Draw' => '5',
                'Symbol' => 'SAR',
                'Code' => NULL,
            ),
            3 => 
            array (
                'id' => 1,
                'Arabic_Name' => 'جنيه مصري',
                'English_Name' => 'Egyptian Pound',
                'created_at' => '2021-06-14 15:44:02',
                'updated_at' => '2022-06-08 17:39:50',
                'Draw' => '1',
                'Symbol' => 'EGP',
                'Code' => NULL,
            ),
        ));
        
        
    }
}