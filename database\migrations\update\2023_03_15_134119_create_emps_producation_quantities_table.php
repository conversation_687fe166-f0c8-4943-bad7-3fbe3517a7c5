<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEmpsProducationQuantitiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('emps_producation_quantities', function (Blueprint $table) {
            $table->id();
            $table->longText('FromQ')->nullable();
			$table->longText('ToQ')->nullable();
			$table->longText('ValueQ')->nullable();
			$table->longText('Emp')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('emps_producation_quantities');
    }
}
