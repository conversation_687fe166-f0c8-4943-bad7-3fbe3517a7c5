<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DevicesTypesiesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('devices_typesies')->delete();
        
        \DB::table('devices_typesies')->insert(array (
            0 => 
            array (
                'id' => 6,
                'Arabic_Name' => 'Default',
                'English_Name' => 'Default',
                'Company' => 14,
                'created_at' => '2022-04-13 02:50:40',
                'updated_at' => '2022-04-13 02:50:40',
            ),
            1 => 
            array (
                'id' => 7,
                'Arabic_Name' => 'Note44',
                'English_Name' => 'Note44',
                'Company' => 14,
                'created_at' => '2022-06-20 21:56:50',
                'updated_at' => '2022-06-20 21:56:50',
            ),
            2 => 
            array (
                'id' => 8,
                'Arabic_Name' => '3',
                'English_Name' => '3',
                'Company' => 15,
                'created_at' => '2022-08-17 02:49:29',
                'updated_at' => '2022-08-17 02:49:29',
            ),
            3 => 
            array (
                'id' => 9,
                'Arabic_Name' => 'S6',
                'English_Name' => 'S6',
                'Company' => 15,
                'created_at' => '2022-08-17 03:12:47',
                'updated_at' => '2022-08-17 03:12:47',
            ),
            4 => 
            array (
                'id' => 10,
                'Arabic_Name' => '44',
                'English_Name' => '44',
                'Company' => 14,
                'created_at' => '2022-08-17 04:35:25',
                'updated_at' => '2022-08-17 04:35:25',
            ),
            5 => 
            array (
                'id' => 11,
                'Arabic_Name' => 'Note44',
                'English_Name' => 'Note44',
                'Company' => 14,
                'created_at' => '2022-08-17 04:36:57',
                'updated_at' => '2022-08-17 04:36:57',
            ),
            6 => 
            array (
                'id' => 12,
                'Arabic_Name' => '33',
                'English_Name' => '33',
                'Company' => 14,
                'created_at' => '2022-08-17 05:27:56',
                'updated_at' => '2022-08-17 05:27:56',
            ),
            7 => 
            array (
                'id' => 13,
                'Arabic_Name' => 'Note44',
                'English_Name' => 'Note44',
                'Company' => 14,
                'created_at' => '2022-08-17 05:37:43',
                'updated_at' => '2022-08-17 05:37:43',
            ),
        ));
        
        
    }
}