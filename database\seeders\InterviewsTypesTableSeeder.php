<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class InterviewsTypesTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('interviews_types')->delete();
        
        \DB::table('interviews_types')->insert(array (
            0 => 
            array (
                'id' => 3,
                'Arabic_Name' => 'تليفونيا',
                'English_Name' => 'تليفونيا',
                'created_at' => '2022-03-08 01:34:24',
                'updated_at' => '2022-03-08 01:34:24',
            ),
            1 => 
            array (
                'id' => 4,
                'Arabic_Name' => 'واتس اب',
                'English_Name' => 'واتس اب',
                'created_at' => '2022-03-08 01:34:32',
                'updated_at' => '2022-03-08 01:34:32',
            ),
            2 => 
            array (
                'id' => 5,
                'Arabic_Name' => 'المكتب',
                'English_Name' => 'المكتب',
                'created_at' => '2022-03-08 01:34:46',
                'updated_at' => '2022-03-08 01:34:46',
            ),
            3 => 
            array (
                'id' => 6,
                'Arabic_Name' => 'لدي العميل',
                'English_Name' => 'لدي العميل',
                'created_at' => '2022-03-08 01:35:05',
                'updated_at' => '2022-03-08 01:35:05',
            ),
            4 => 
            array (
                'id' => 7,
                'Arabic_Name' => 'عرض سعر',
                'English_Name' => 'عرض سعر',
                'created_at' => '2022-03-08 01:36:05',
                'updated_at' => '2022-03-08 01:36:05',
            ),
            5 => 
            array (
                'id' => 8,
                'Arabic_Name' => 'تحصيل',
                'English_Name' => 'تحصيل',
                'created_at' => '2022-03-08 01:36:36',
                'updated_at' => '2022-03-08 01:36:36',
            ),
        ));
        
        
    }
}