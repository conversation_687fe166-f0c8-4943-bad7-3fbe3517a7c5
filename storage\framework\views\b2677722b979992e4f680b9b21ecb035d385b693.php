       
<?php $__env->startSection('content'); ?>
<title><?php echo e(trans('admin.Contact')); ?></title>
<?php
use App\Models\MainEComDesign;
use App\Models\SupPagesEComDesign;
$main=MainEComDesign::orderBy('id','desc')->first();
$sub=SupPagesEComDesign::orderBy('id','desc')->first();
?>

 <style>
.page-title-area {
    background-color: <?php echo e($main->Breadcumb_BG_Color); ?> !important;
}
     
    .contact-area {
      background-color: <?php echo e($main->Sub_Page_BG_Color); ?> !important;
} 
</style>

        <!-- Start Page Banner -->
        <div class="page-title-area">
            <div class="container">
                <div class="page-title-content">
                    <h2 style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important"><?php echo e(trans('admin.Contact')); ?></h2>

                    <ul>
                        <li><a style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important" href="<?php echo e(url('/')); ?>"><?php echo e(trans('admin.Home')); ?></a></li>
                        <li style="color: <?php echo e($main->Breadcumb_Txt_Color); ?> !important"><?php echo e(trans('admin.Contact')); ?></li>
                    </ul>
                </div>
            </div>
        </div>
        <!-- End Page Banner -->

        <!-- Start Contact Area -->
        <section class="contact-area ptb-50">
            <div class="container">
                <div class="row">
                    <div class="col-lg-7 col-md-12">
                        <div class="contact-form">
                            <div class="tile">
                                <h3 style="color: <?php echo e($sub->Contact_Title_Color); ?> !important"><?php echo e(trans('admin.Leave_us_a_Message')); ?></h3>
                            </div>

                            <form   action="<?php echo e(url('PostMsgRqst')); ?>" method="post"  id="contactForm">
                                 <?php echo csrf_field(); ?>
                                <div class="row">
                                    <div class="col-lg-12 col-md-12">
                                        <div class="form-group">
                                            <label style="color: <?php echo e($sub->Contact_Title_Color); ?> !important"><?php echo e(trans('admin.Message')); ?>*</label>

                                            <textarea style="border-color: <?php echo e($sub->Contact_Form_Input_Border_Color); ?> !important; color: <?php echo e($sub->Contact_Form_Input_Txt_Color); ?> !important"   name="Msg" placeholder="<?php echo e(trans('admin.Message')); ?>" cols="30" rows="5" class="form-control"></textarea>
                                            <div class="help-block with-errors"></div>
                                        </div>
                                    </div>

                                    <div class="col-lg-6 col-md-6">
                                        <div class="form-group">
                                            <label style="color: <?php echo e($sub->Contact_Title_Color); ?> !important"><?php echo e(trans('admin.Name')); ?> *</label>




                                            <input style="border-color: <?php echo e($sub->Contact_Form_Input_Border_Color); ?> !important; color: <?php echo e($sub->Contact_Form_Input_Txt_Color); ?> !important" type="text" class="form-control" name="Name" placeholder="<?php echo e(trans('admin.Name')); ?>" value="<?php echo e(old('Name')); ?>" required >
                                    
                                        </div>
                                    </div>

                                    <div class="col-lg-6 col-md-6">
                                        <div class="form-group">
                                            <label style="color: <?php echo e($sub->Contact_Title_Color); ?> !important"><?php echo e(trans('admin.Email')); ?>*</label>

                                            <input style="border-color: <?php echo e($sub->Contact_Form_Input_Border_Color); ?> !important; color: <?php echo e($sub->Contact_Form_Input_Txt_Color); ?> !important" type="email"  class="form-control" name="Email" placeholder="<?php echo e(trans('admin.Email')); ?>" value="<?php echo e(old('Email')); ?>" required >
                                
                                        </div>
                                    </div>

                                    <div class="col-lg-6 col-md-12">
                                        <div class="form-group">
                                            <label style="color: <?php echo e($sub->Contact_Title_Color); ?> !important"><?php echo e(trans('admin.Phone')); ?>*</label>

                                            <input style="border-color: <?php echo e($sub->Contact_Form_Input_Border_Color); ?> !important; color: <?php echo e($sub->Contact_Form_Input_Txt_Color); ?> !important" type="tel"  class="form-control" name="Phone" placeholder="<?php echo e(trans('admin.Phone')); ?>" value="<?php echo e(old('Phone')); ?>" required >
                                  
                                        </div>
                                    </div>

                                    <div class="col-lg-6 col-md-12">
                                        <div class="form-group">
                                            <label style="color: <?php echo e($sub->Contact_Title_Color); ?> !important"><?php echo e(trans('admin.Subject')); ?>*</label>

                                            <input style="border-color: <?php echo e($sub->Contact_Form_Input_Border_Color); ?> !important; color: <?php echo e($sub->Contact_Form_Input_Txt_Color); ?> !important" type="text"  class="form-control"  name="Subject" placeholder="<?php echo e(trans('admin.Subject')); ?>" value="<?php echo e(old('Subject')); ?>" required>
                                  
                                        </div>
                                    </div>

                                    <div class="col-lg-12 col-md-12">
                                        <button type="submit" class="default-btn">
                                   
                                            <?php echo e(trans('admin.Send_Message')); ?>

                                            <span></span>
                                        </button>
                                        <div id="msgSubmit" class="h3 text-center hidden"></div>
                             
                                    </div>
                                </div>
                            </form>
                            
                          
      
                        </div>
                    </div>

                    <div class="col-lg-5 col-md-12">
                        <div class="contact-information">
                            <h3 style="color: <?php echo e($sub->Contact_Title_Color); ?> !important"><?php echo e(app()->getLocale() == 'ar' ?$Contact->Arabic_Title :$Contact->English_Title); ?></h3>
                            <p  style="color: <?php echo e($sub->Contact_Txt_Color); ?> !important"> <?php echo e(app()->getLocale() == 'ar' ?$Contact->Arabic_Desc :$Contact->English_Desc); ?> </p>

                            <ul class="contact-list">
                                <li><i class='bx bx-map'></i> <?php echo e(trans('admin.Address')); ?>: <span  style="color: <?php echo e($sub->Contact_Txt_Color); ?> !important">  <?php echo e(app()->getLocale() == 'ar' ?$Contact->Arabic_Address :$Contact->English_Address); ?></span></li>
                                <li style="color: <?php echo e($sub->Contact_Title_Color); ?> !important"><i style="color: <?php echo e($sub->Contact_Title_Color); ?> !important" class='bx bx-phone-call'></i> <?php echo e(trans('admin.Phone')); ?>: 
                                    
                                    <a  style="color: <?php echo e($sub->Contact_Txt_Color); ?> !important" href="tel: <?php echo e($Contact->Phone1); ?>"> <?php echo e($Contact->Phone1); ?> </a> /
                                    <a  style="color: <?php echo e($sub->Contact_Txt_Color); ?> !important" href="tel:    <?php echo e($Contact->Phone2); ?>">    <?php echo e($Contact->Phone2); ?> </a>
                                
                                </li>
                                <li style="color: <?php echo e($sub->Contact_Title_Color); ?> !important"><i style="color: <?php echo e($sub->Contact_Title_Color); ?> !important" class='bx bx-envelope'></i> <?php echo e(trans('admin.Email')); ?>: <a href="<?php echo e($Contact->Email); ?>"><span  style="color: <?php echo e($sub->Contact_Txt_Color); ?> !important" class="__cf_email__" data-cfemail="<?php echo e($Contact->Email); ?>"><?php echo e($Contact->Email); ?></span></a></li>    
                                
                                <li style="color: <?php echo e($sub->Contact_Title_Color); ?> !important"><i style="color: <?php echo e($sub->Contact_Title_Color); ?> !important" class='bx bx-envelope'></i> <?php echo e(trans('admin.Opening_Hours')); ?>: <a href="<?php echo e($Contact->Email); ?>"><span style="color: <?php echo e($sub->Contact_Txt_Color); ?> !important" class="__cf_email__" data-cfemail="<?php echo e($Contact->Email); ?>">  <?php echo e($Contact->Opening_Hours); ?></span></a></li>
                            </ul>

                          
 
     
    
                           
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- End Contact Area -->

        <!-- Map -->
        <div id="map">
      <?php echo $Contact->Map; ?>

        </div>
        <!-- End Map -->
        

<style>
       .contact-form #contactForm .default-btn{
        
        background: <?php echo e($sub->Contact_Form_Button_BG_Color); ?>  !important;
        color: <?php echo e($sub->Contact_Form_Button_Txt_Color); ?>  !important;
    }
    
    
     .contact-form #contactForm .default-btn:hover{
        
        background: <?php echo e($sub->Contact_Form_Button_Hover_BG_Color); ?>  !important;
        color: <?php echo e($sub->Contact_Form_Button_Hover_Txt_Color); ?>  !important;
    }
    
   .contact-form #contactForm .default-btn:hover i {

        color: <?php echo e($sub->Contact_Form_Button_Hover_Txt_Color); ?>  !important;
}
    
    
    .contact-form #contactForm .default-btn:hover span {
        
             background: <?php echo e($sub->Contact_Form_Button_Hover_BG_Color); ?>  !important;
        color: <?php echo e($sub->Contact_Form_Button_Hover_Txt_Color); ?>  !important;
        
    }
</style>

<?php $__env->stopSection(); ?> 
<?php echo $__env->make('site.index', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\ost-erp-online\resources\views/site/Contact.blade.php ENDPATH**/ ?>