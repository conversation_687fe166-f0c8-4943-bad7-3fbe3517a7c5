<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAttendencePolicyEmpsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('attendence_policy_emps', function (Blueprint $table) {
            $table->id();
              $table->longText('From')->nullable();
			$table->longText('To')->nullable();
			$table->longText('Discount')->nullable();
			$table->longText('Emp')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('attendence_policy_emps');
    }
}
