<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class CustomersTableSeeder extends Seeder
{

    /**
     * Auto generated seed file
     *
     * @return void
     */
    public function run()
    {
        

        \DB::table('customers')->delete();
        
        \DB::table('customers')->insert(array (
            0 => 
            array (
                'id' => 30,
                'Code' => '2',
                'Date' => NULL,
                'Name' => 'عميل مخزن المخزون',
                'Price_Level' => '1',
                'Phone' => NULL,
                'email' => NULL,
                'password' => NULL,
                'ID_Number' => NULL,
                'Address' => NULL,
                'Qualifications' => NULL,
                'Birthdate' => NULL,
                'Social_Status' => NULL,
                'Passport_Number' => NULL,
                'Company_Name' => NULL,
                'Commercial_Registration_No' => NULL,
                'Tax_Card_No' => NULL,
                'Bank_Account' => NULL,
                'Image' => NULL,
                'Next_Time' => NULL,
                'Executions_Status' => NULL,
                'Governrate' => NULL,
                'City' => NULL,
                'Responsible' => NULL,
                'Activity' => NULL,
                'Campagin' => NULL,
                'ClientStatus' => NULL,
                'Account' => 218,
                'User' => 1,
                'created_at' => '2022-03-08 02:53:23',
                'updated_at' => '2022-03-08 02:53:23',
                'Platform' => NULL,
                'Contract_Start' => NULL,
                'Contract_End' => NULL,
                'Group' => NULL,
                'code' => NULL,
                'country' => NULL,
                'Tax_Registration_Number' => NULL,
                'Tax_activity_code' => NULL,
                'work_nature' => NULL,
                'Place' => NULL,
                'Nationality' => NULL,
                'Buliding_Num' => NULL,
                'Street' => NULL,
                'Postal_Code' => NULL,
                'tax_magistrate' => NULL,
                'Floor' => NULL,
                'Room' => NULL,
                'Landmark' => NULL,
                'Add_Info' => NULL,
            ),
            1 => 
            array (
                'id' => 588,
                'Code' => '3',
                'Date' => '2022-05-22',
                'Name' => 'Default',
                'Price_Level' => '1',
                'Phone' => '1234',
                'email' => NULL,
                'password' => NULL,
                'ID_Number' => NULL,
                'Address' => NULL,
                'Qualifications' => NULL,
                'Birthdate' => NULL,
                'Social_Status' => NULL,
                'Passport_Number' => NULL,
                'Company_Name' => NULL,
                'Commercial_Registration_No' => NULL,
                'Tax_Card_No' => NULL,
                'Bank_Account' => NULL,
                'Image' => NULL,
                'Next_Time' => NULL,
                'Executions_Status' => NULL,
                'Governrate' => NULL,
                'City' => NULL,
                'Responsible' => NULL,
                'Activity' => NULL,
                'Campagin' => NULL,
                'ClientStatus' => NULL,
                'Account' => 832,
                'User' => 11,
                'created_at' => '2022-05-22 10:37:58',
                'updated_at' => '2022-05-22 10:37:58',
                'Platform' => NULL,
                'Contract_Start' => NULL,
                'Contract_End' => NULL,
                'Group' => NULL,
                'code' => NULL,
                'country' => NULL,
                'Tax_Registration_Number' => NULL,
                'Tax_activity_code' => NULL,
                'work_nature' => NULL,
                'Place' => NULL,
                'Nationality' => NULL,
                'Buliding_Num' => NULL,
                'Street' => NULL,
                'Postal_Code' => NULL,
                'tax_magistrate' => NULL,
                'Floor' => NULL,
                'Room' => NULL,
                'Landmark' => NULL,
                'Add_Info' => NULL,
            ),
            2 => 
            array (
                'id' => 587,
                'Code' => '2',
                'Date' => NULL,
                'Name' => 'عميل نقدي',
                'Price_Level' => '1',
                'Phone' => NULL,
                'email' => NULL,
                'password' => NULL,
                'ID_Number' => NULL,
                'Address' => NULL,
                'Qualifications' => NULL,
                'Birthdate' => NULL,
                'Social_Status' => NULL,
                'Passport_Number' => NULL,
                'Company_Name' => NULL,
                'Commercial_Registration_No' => NULL,
                'Tax_Card_No' => NULL,
                'Bank_Account' => NULL,
                'Image' => NULL,
                'Next_Time' => NULL,
                'Executions_Status' => NULL,
                'Governrate' => NULL,
                'City' => NULL,
                'Responsible' => NULL,
                'Activity' => NULL,
                'Campagin' => NULL,
                'ClientStatus' => NULL,
                'Account' => 34,
                'User' => 1,
                'created_at' => NULL,
                'updated_at' => NULL,
                'Platform' => NULL,
                'Contract_Start' => NULL,
                'Contract_End' => NULL,
                'Group' => '3',
                'code' => NULL,
                'country' => NULL,
                'Tax_Registration_Number' => NULL,
                'Tax_activity_code' => NULL,
                'work_nature' => NULL,
                'Place' => NULL,
                'Nationality' => NULL,
                'Buliding_Num' => NULL,
                'Street' => NULL,
                'Postal_Code' => NULL,
                'tax_magistrate' => NULL,
                'Floor' => NULL,
                'Room' => NULL,
                'Landmark' => NULL,
                'Add_Info' => NULL,
            ),
        ));
        
        
    }
}