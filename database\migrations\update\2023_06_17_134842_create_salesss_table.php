<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSalesssTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

             
             Schema::table('sales', function (Blueprint $table) {
    $table->unsignedInteger('InstallCompany')->change();
    $table->foreign('InstallCompany')->references('id')->on('installment_companies');
});
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('comments_clients');
    }
}
