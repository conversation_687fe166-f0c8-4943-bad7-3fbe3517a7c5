<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShippingListTicketsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('shipping_list_tickets', function (Blueprint $table) {
            $table->id();
              $table->longText('Shipping_List')->nullable();
              $table->longText('Code')->nullable();
              $table->longText('Sender_Name')->nullable();
              $table->longText('Addressees_Name')->nullable();
              $table->longText('Total_Qty')->nullable();
              $table->longText('Total_Price')->nullable();
              $table->longText('Payment_Method')->nullable();
              $table->longText('Notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('shipping_list_tickets');
    }
}
